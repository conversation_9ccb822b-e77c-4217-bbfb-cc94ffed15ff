version: '3'

tasks:
  default:
    desc: xxx
    cmds:
      - pnpm add -g typescript
      - pnpm add -g ts-node
      - pnpm add -g tsx
      - pnpm install
      - task: picDir
      - task: PrintPicDirMap
      - pnpm build

  install:
    desc: 安装项目依赖
    cmds:
      - pnpm install

  start:
    desc: 启动开发服务器
    cmds:
      - yarn start
      - defer: rm -rf .docusaurus

  build:
    desc: 构建生产版本
    cmds:
      - pnpm build
    sources:
      - src/**
    generates:
      - build/

  serve:
    desc: 服务构建后的静态文件
    cmds:
      - yarn serve

  clean:
    desc: 清理构建文件
    cmds:
      - rm -rf build/

  deploy:
    desc: Deploy To Cloudflare
    cmds:
      - pnpm build
      - npx wrangler pages deploy "build" --project-name="blog" --branch="gh-pages"
      - rm -rf build/ .docusaurus
    depends:
      - build

  lint:
    desc: 代码质量检查
    cmds:
      - yarn lint

  test:
    desc: 运行测试
    cmds:
      - yarn test

  bundle:
    desc: docusaurus bundle
    cmds:
      - docusaurus build --bundle-analyzer

  pre-commit:
    desc: pre-commit
    cmds:
      - pre-commit run --all-files


  picDir:
    desc: Generate PicDirMap.json from Cloudflare R2 storage
    cmd: |
      echo "Generating PicDirMap.json from Cloudflare R2..."
      mkdir -p web/data
      rclone lsjson --recursive --files-only r2:docs-images | \
      jq -r '.[] | select(.Name | test("\\.(svg|jpg|jpeg|png|gif|webp)$"; "i")) | .Path' | \
      jq -R -s '
      split("\n") |
      map(select(length > 0)) |
      reduce .[] as $path ({};
      ($path | split("/") | .[:-1] | join("/") | if . == "" then "." else . end) as $dir |
      .[$dir] = (.[$dir] // []) + [$path]
      )
      ' > web/data/PicDirMap.json
      echo "PicDirMap.json generated successfully at web/data/PicDirMap.json"


  PrintPicDirMap:
    cmds:
      - cat web/data/PicDirMap.json

  zzz:
    cmds:
      - 'echo {{OS}}'
      - 'echo {{uuid}}'
      - 'echo {{add 1 2}}'
      - 'echo {{now}}'
      - 'echo {{.TASK}}'
    silent: true
