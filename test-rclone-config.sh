#!/bin/bash

# Test script to verify rclone configuration for Cloudflare R2
echo "Testing rclone configuration for Cloudflare R2..."

# Create rclone config directory
mkdir -p ~/.config/rclone

# Create rclone configuration
cat > ~/.config/rclone/rclone.conf << EOF
[r2]
type = s3
provider = Cloudflare
access_key_id = c4bbed2e818a1982300dee48e646bc4d
secret_access_key = 16479efdae33f0f037d25a44f1f9561335d1243ee661f8c1ad6f749f95d6f0cb
endpoint = https://96540bd100b82adba941163704660c31.r2.cloudflarestorage.com
acl = private
EOF

echo "Rclone configuration created."

# Test rclone connection
echo "Testing rclone connection to R2..."
rclone lsd r2:

echo "Testing file listing in docs-images bucket..."
rclone lsjson --recursive --files-only r2:docs-images | head -5

echo "Test completed."
