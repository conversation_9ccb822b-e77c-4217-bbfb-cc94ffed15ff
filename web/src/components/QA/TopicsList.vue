<script lang="ts" setup>
import type { TopicItem } from "../../types/data";
import picDirMap from "@data/PicDirMap.json";

import { computed } from "vue";

import { ImageOutline as ImageOutlineIcon } from "@vicons/ionicons5";
import {
  NButton,
  NCard,
  NCollapse,
  NCollapseItem,
  NDivider,
  NIcon,
  NImage,
  NImageGroup
} from "naive-ui";

import { renderMarkdown } from "#/utils/markdown";
import TableRenderer from "./TableRenderer.vue";
import TablesRenderer from "./TablesRenderer.vue";

interface Props {
  bordered?: boolean;
  /** 是否默认展开所有问题 */
  defaultExpandAll?: boolean;
  items: TopicItem[];
  title?: string;
}

let ImageHostURL = "https://cdn.lucc.dev"

const props = withDefaults(defineProps<Props>(), {
  defaultExpandAll: true
});

const expandedNames = computed(() => {
  if (!props.defaultExpandAll) return [];
  // 只有当项目有子问题(qs)、图片(pic)、表格(table/tables)，且isFold不为true时才默认展开
  return props.items
    .map((item, index) => (!item.isFold && (item.qs?.length || item.table?.length || item.tables?.length || item.des) ? index : null))
    .filter((index): index is number => index !== null);
});

// 新增：提取图片名的方法
function getPicName(url: string): string {
  if (!url) return "";
  const parts = url.split("/");
  return parts[parts.length - 1];
}


// 获取目录下的所有图片
function getDirPics(dirPath: string): string[] {
  if (!dirPath || !picDirMap[dirPath]) return [];
  return picDirMap[dirPath].map(file => `${ImageHostURL}/${file}`);
}

// 背景模式状态管理
// 已移除forceWhiteBackground

// 检测图片是否为SVG格式
function isSvgImage(url: string): boolean {
  return url.toLowerCase().endsWith('.svg');
}

// 获取智能背景类 - 根据图片类型选择背景
function getSmartBackgroundClass(url: string) {
  if (isSvgImage(url)) {
    // SVG图片在暗色模式下强制使用白色背景
    return 'bg-white dark:bg-white';
  } else {
    // 其他图片使用自适应背景
    return 'bg-white dark:bg-gray-800';
  }
}

// 获取所有图片
function getAllPics(item: TopicItem): string[] {
  const pics: string[] = [];
  if (item.picDir) {
    pics.push(...getDirPics(item.picDir));
  }
  return pics;
}
</script>

<template>
  <NCard :title="title" :bordered="bordered ?? false">
    <div class="flex flex-col">
      <NCollapse :default-expanded-names="expandedNames">
        <NCollapseItem
          v-for="(item, index) in items"
          :key="index"
          :name="index"
          :class="{ 'important-topic': item.isX }"
        >
          <template #header>
            <NButton
              v-if="item.url"
              text
              type="primary"
              tag="a"
              :href="item.url"
              target="_blank"
              class="question-title text-base font-medium"
              :class="{ 'important-topic': item.isX }"
            >
              <div v-html="renderMarkdown(item.topic)"></div>
            </NButton>
            <div
              v-else
              class="question-title"
              :class="{ 'important-topic': item.isX }"
              v-html="renderMarkdown(item.topic)"
            ></div>
          </template>
          <div class="flex flex-col gap-4">
            <!-- 图片展示 -->
            <div v-if="item.picDir" class="flex flex-col gap-4">
              <NImageGroup>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  <div
                    v-for="pic in getAllPics(item)"
                    :key="pic"
                    class="flex flex-col items-center"
                  >
                    <div
                      :class="['w-full aspect-video cursor-pointer overflow-hidden rounded-lg border border-gray-200 dark:border-gray-600', getSmartBackgroundClass(pic)]">
                      <NImage
                        :src="pic"
                        :alt="getPicName(pic)"
                        lazy
                        class="h-full w-full"
                        object-fit="contain"
                        show-toolbar
                      >
                        <template #error>
                          <NIcon :size="48" class="text-gray-400 dark:text-gray-500">
                            <ImageOutlineIcon/>
                          </NIcon>
                        </template>
                        <template #placeholder>
                          <div
                            class="flex-center h-full w-full bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400">
                            Loading
                          </div>
                        </template>
                      </NImage>
                    </div>
                    <div
                      class="text-center text-sm text-gray-600 dark:text-gray-300 mt-2 w-full truncate">
                      {{ getPicName(pic) }}
                    </div>
                  </div>
                </div>
              </NImageGroup>
            </div>

            <!-- 问题描述 -->
            <div
              v-if="item.des"
              class="markdown-content"
              v-html="renderMarkdown(item.des)"
            ></div>

            <!-- 表格展示 -->
            <TableRenderer v-if="item.table?.length" :data="item.table"/>

            <!-- 多表格展示 -->
            <TablesRenderer v-if="item.tables?.length" :tables="item.tables"/>


            <!-- 3W3H 渲染 -->
            <div v-if="item.why || item.what || item.ww || item.hti || item.htu || item.hto"
                 class="markdown-content">


              <div v-if="item.why" class="mb-4">
                <NDivider title-placement="left">[why]</NDivider>
                <ul>
                  <li v-for="(reason, idx) in item.why" :key="idx"
                      v-html="renderMarkdown(reason)"></li>
                </ul>
              </div>
              <div v-if="item.what" class="mb-4">
                <NDivider title-placement="left">[what]</NDivider>
                <ul>
                  <li v-for="(detail, idx) in item.what" :key="idx"
                      v-html="renderMarkdown(detail)"></li>
                </ul>
              </div>
              <div v-if="item.ww" class="mb-4">
                <NDivider title-placement="left">[when/where]</NDivider>
                <ul>
                  <li v-for="(point, idx) in item.ww" :key="idx"
                      v-html="renderMarkdown(point)"></li>
                </ul>
              </div>
              <div v-if="item.htu" class="mb-4">
                <NDivider title-placement="left">[htu]</NDivider>
                <ul>
                  <li v-for="(usage, idx) in item.htu" :key="idx"
                      v-html="renderMarkdown(usage)"></li>
                </ul>
              </div>
              <div v-if="item.hti" class="mb-4">
                <NDivider title-placement="left">[hti]</NDivider>
                <ul>
                  <li v-for="(info, idx) in item.hti" :key="idx" v-html="renderMarkdown(info)"></li>
                </ul>
              </div>
              <div v-if="item.hto?.length" class="mb-4">
                <NDivider title-placement="left">[hto]</NDivider>
                <ul>
                  <li v-for="(other, idx) in item.hto" :key="idx"
                      v-html="renderMarkdown(other)"></li>
                </ul>
              </div>
            </div>

            <!-- 子问题列表 -->
            <div v-if="item.qs?.length" class="markdown-content">
              <ul>
                <li v-for="(q, idx) in item.qs" :key="idx" v-html="renderMarkdown(q)"></li>
              </ul>
            </div>


          </div>
        </NCollapseItem>
      </NCollapse>
    </div>
  </NCard>
</template>


<style scoped>
.n-collapse :deep(.n-collapse-item__header) {
  font-size: 16px;
  font-weight: 500;
}

.n-collapse :deep(.n-collapse-item__header-main) {
  padding: 12px 16px;
}

.n-collapse :deep(.n-collapse-item__content-inner) {
  padding: 16px;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.markdown-content) {
  @apply text-sm text-gray-600 dark:text-gray-300;
}

:deep(.markdown-content p) {
  margin: 0.5em 0;
}

:deep(.markdown-content ul) {
  list-style-type: disc;
  padding-left: 1.5em;
  margin: 0.5em 0;
}

:deep(.markdown-content ol) {
  list-style-type: decimal;
  padding-left: 1.5em;
  margin: 0.5em 0;
}

:deep(.markdown-content a) {
  color: var(--primary-color);
  text-decoration: none;
}

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}

:deep(.markdown-content code) {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.dark :deep(.markdown-content code) {
  background-color: rgba(255, 255, 255, 0.1);
  color: theme('colors.gray.200');
}

:deep(.markdown-content pre code) {
  display: block;
  padding: 1em;
  overflow-x: auto;
}

.question-title {
  max-width: 100%;
  white-space: normal;
  word-break: break-word;
  text-align: left;
}

.important-topic {
  background-color: #fff8e1;
}

.important-topic :deep(.n-collapse-item__header) {
  background-color: #fff8e1;
}

.important-topic :deep(.n-collapse-item__content) {
  background-color: #fff8e1;
}

/* Dark mode 适配 */
.dark .important-topic {
  background-color: #4a3728;
}

.dark .important-topic :deep(.n-collapse-item__header) {
  background-color: #4a3728;
}

.dark .important-topic :deep(.n-collapse-item__content) {
  background-color: #4a3728;
}

/* 透明背景图片的棋盘格背景 */
.transparent-bg {
  background-color: #ffffff;
  background-image: linear-gradient(45deg, #f5f5f5 25%, transparent 25%),
  linear-gradient(-45deg, #f5f5f5 25%, transparent 25%),
  linear-gradient(45deg, transparent 75%, #f5f5f5 75%),
  linear-gradient(-45deg, transparent 75%, #f5f5f5 75%);
  background-size: 16px 16px;
  background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}

/* 暗色模式下强制使用白色背景确保SVG清晰可见 */
.dark .transparent-bg {
  background-color: #ffffff;
  background-image: linear-gradient(45deg, #f5f5f5 25%, transparent 25%),
  linear-gradient(-45deg, #f5f5f5 25%, transparent 25%),
  linear-gradient(45deg, transparent 75%, #f5f5f5 75%),
  linear-gradient(-45deg, transparent 75%, #f5f5f5 75%);
  background-size: 16px 16px;
  background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}


/* 移动端适配 */
/* The grid is now responsive using Tailwind CSS classes directly on the element.
   Removed the explicit media query here as Tailwind handles it more effectively.
   If you need to customize breakpoints further, refer to Tailwind CSS documentation. */
</style>
