name: Deploy to Cloudflare Pages

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@main
        with:
          token: ${{ secrets.PAT }}

      - uses: pnpm/action-setup@v4
      - uses: actions/setup-go@main
      - uses: arduino/setup-task@main
        with:
          version: 3.x
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      # Install rclone
      - name: Install rclone
        run: |
          curl -O https://downloads.rclone.org/rclone-current-linux-amd64.zip
          unzip rclone-current-linux-amd64.zip
          sudo mv rclone-*-linux-amd64/rclone /usr/local/bin/
          sudo chown root:root /usr/local/bin/rclone
          sudo chmod 755 /usr/local/bin/rclone
          rclone version

      # Install jq (usually pre-installed on ubuntu-latest, but ensure it's available)
      - name: Install jq
        run: sudo apt-get update && sudo apt-get install -y jq

      # Configure rclone for Cloudflare R2
      - name: Configure rclone
        run: |
          mkdir -p ~/.config/rclone
          cat > ~/.config/rclone/rclone.conf << EOF
          [r2]
          type = s3
          provider = Cloudflare
          access_key_id = c4bbed2e818a1982300dee48e646bc4d
          secret_access_key = 16479efdae33f0f037d25a44f1f9561335d1243ee661f8c1ad6f749f95d6f0cb
          endpoint = https://96540bd100b82adba941163704660c31.r2.cloudflarestorage.com
          acl = private
          EOF

      - run: pnpm add -g tsx

      - run: task y2m
      - run: task docs

      - uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          command: pages deploy "./web/dist" --project-name="docs" --branch="gh-pages"
