name: Deploy to Cloudflare Pages

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@main
        with:
          token: ${{ secrets.PAT }}

      - uses: pnpm/action-setup@v4
      - uses: actions/setup-go@main
      - uses: arduino/setup-task@main
        with:
          version: 3.x
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - run: pnpm add -g tsx

      - run: task y2m
      - run: task docs

      - uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          command: pages deploy "./web/dist" --project-name="docs" --branch="gh-pages"
