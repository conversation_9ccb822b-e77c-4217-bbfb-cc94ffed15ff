

## DocsV4

### Feat

- 把images从R2挪到了本地
- 移除了blog项目（之前Docs是admin，blog是docusaurus），现在直接把blog作为docs里的topic了。另外，用github issue作为blog（更多是 archive）



feeds //all websites/

相较于之前的workflow，最大的区别：

1、现在没有scratch.md,也没有Diary.md和TASK.md了，都换成YAML了。更精细化了。

2、其实现在也没啥workflow了，之前的scratch可以作为一个buffer来缓冲各种资料。定期处理到docs里的相应

url 文件夹，但是现在没有了，也就没有workflow了。

3、关于bog,之前是放到 TASK.md里(或者 embeds到 TASK.md.)。现在没有 TASK.md了,不着急,在dact项目里写好 之后，再sync到blog。

无关workflow的一些点：

其实可能也不需要再把url贴回去7、可以说，现在整个dz项目里，除了dar文件夹里还有一些mnd文件。其他的都是yawd文件了，然后通过doar—fred

或者用react componenfs直接渲染yaml文件。 books




### zzz



现在想来，之前确实是走了弯路了，之前没有topics（以及当时还是blog，而非admin），为了能把相应技术与repo一一对应，以方便查看相应Qs。比如说，为了能直接记录Netfilter相关Qs，就需要专门找个name（实际上是前缀）为netfiler的repo。类似这种，比比皆是。





【2025-06-27】

用了几天时间删除了大量之前整理的repo，但是这整个过程并非全无意义。就像是之前极简生活，扔掉了很多之前买的东西一样。

比如说：

1、之前存了 r2-upload-action，其实直接用rclone不就行了？
2、把所有 cf workers都删掉了，当时基于FOMO添加了这些，但是现在一看，都TM没用。  调查

从1962个repo，删到现在只有829个。
