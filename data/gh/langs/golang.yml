---

# FIXME [RezaSi/go-interview-practice: Go Interview Practice is a series of coding challenges to help you prepare for technical interviews in Go. Solve problems, submit your solutions, and receive instant feedback with automated testing. Track your progress with per-challenge scoreboards and improve your coding skills step by step.](https://github.com/RezaSi/go-interview-practice)

# FIXME [cch123/golang-notes: Go source code analysis(zh-cn)](https://github.com/cch123/golang-notes)

# FIXME
#- url: https://github.com/hwholiday/learning_tools
#  des: 很杂的一个项目
#- url: https://github.com/avelino/awesome-go
#  des: awesome-golang

# TODO
#- url: https://github.com/lotusirous/go-concurrency-patterns
#  des: concurrency patterns in golang.


#- url: https://github.com/RichardKnop/machinery
#
#- url: https://github.com/appleboy/gorush
#  des: ???
#
#- url: https://github.com/dustin/go-humanize
#  des: human friendly formatter
#
#- url: https://github.com/adnanh/webhook
#
#- url: https://github.com/atotto/clipboard
#  des: clipboard for golang
#
#
#- url: https://github.com/saintfish/chardet
#  des: used to detect charset of text.
#- url: https://github.com/adaptive-scale/dbchaos
#  des: Database Stress-Test Tool.
#
#- url: https://github.com/mazrean/formstream
#  des: ???
#- url: https://github.com/hellofresh/health-go
#  des: Library to provide basic healthcheck functionality to Go applications.
#
#- url: https://github.com/shirou/gopsutil
#  des: psutil for golang
#
#- url: https://github.com/hashicorp/go-plugin
#  des: ??? 没看懂
#
#- url: https://github.com/nitishm/go-rejson
#  des: ???
#
#- url: https://github.com/spf13/afero
#  des: 类似kernel VFS那样的，将不同文件系统统一抽象为一个通用接口。当然，这个不是kernel层面，而只是应用层面的抽象
#
#- url: https://github.com/gabriel-vasile/mimetype
#  des: used to detect filetype. 与golang通常用http.DetectContentType(buffer)获取mimetype不同的是，mimetype会读取文件的字节，在MIME这个二叉树结构的数据（各种filetype的magic number）中进行前序遍历，而不需要依赖文件名或其他元数据。相比之下，mimetype比 DetectContentType 和 filetype 的性能都更好（README里贴了benchmark）。并且，mimetype 提供了更易用的API可以直接使用，使用DetectContentType函数则需要自己打开文件并读取字节，略微繁琐。
#
#
#- url: https://github.com/risor-io/risor
#  des: Fast and flexible scripting for Go developers and DevOps.
#
#- url: https://github.com/cerbos/cerbos
#  des: # [How to implement authorization using Cerbos in Go | Cerbos](https://www.cerbos.dev/blog/how-to-implement-authorization-in-go)
#
#- url: https://github.com/chaosblade-io/chaosblade
#- url: https://github.com/chaos-mesh/chaos-mesh
#- url: https://github.com/Netflix/chaosmonkey



# TODO [mattn/go-generics-example: Example code for Go generics](https://github.com/mattn/go-generics-example)



- type: golang
  tag: langs
  score: 5
  using:
    url: https://github.com/golang/go
    doc: https://go.dev/doc/ # 可以看看spec
  repo:
    - url: https://github.com/uber-go/atomic
      score: 5

    - url: https://github.com/golang/sync
      des: 【2025-07-02】conc可以替代sycn的ErrorGroup, Sema，无法替代 singleflight和CyclicBarrier。
      score: 5

    - url: https://github.com/knadh/koanf
      score: 4
      des: 用viper的目的是是兼容本地配置和远程配置中心两种模式，因为可以无感从local切换到etcd. viper可以读取JSON, TOML, YAML, HCL, .env, and Java properties等格式的配置文件.
      record:
        - 【2025-08-07】移除“【技术选型】conf处理”（移除了【jinzhu/configor（已经EOL了）】、【godotenv（只支持解析.env文件）】），并用【koanf】代替【viper】。相较之下，前者依赖更少（更轻量）、可拓展性更好。同样也支持JSON, YAML, TOML, HCL等常用配置文件。
      qs:
        - 使用viper实现yaml配置文件的合并 # [使用viper实现yaml配置文件的合并](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453499&idx=1&sn=8f6234b80b49df964dfe5b1070c7fb35)
        - viper读取单文件中的多个separate yaml


    # 以下这些已经整理到 golangci-lint.yml 的rules了，所以仅供参考
    # [TOP 20 Go最佳实践](https://colobu.com/2023/11/17/golang-quick-reference-top-20-best-coding-practices/)
    # [不要写破坏性的 Go 库](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651450350&idx=2&sn=3dd4cf2d90419c0543b66ab951edeb29&poc_token=HHy3mmijsS_n4X1Cqlhb6w97YbSH4b1fl-L9-fzn)
    # [Go中的一些优化笔记，简单而不简单](https://mp.weixin.qq.com/s/X8c6ZIJdBFptYA9CRj6wnA)
    # [[长文]从《100 Go Mistakes》我总结了什么？ - luozhiyun`s Blog](https://www.luozhiyun.com/archives/797)
    # [深度阅读之《100 Go Mistakes and How to Avoid Them》 | qcrao 的博客](https://qcrao.com/post/100-go-mistakes-reading-notes/)
    # [Chinese (Simplified) Version - 100 Go Mistakes and How to Avoid Them](https://100go.co/zh/)
    # [secguide/Go安全指南.md at main · Tencent/secguide](https://github.com/Tencent/secguide/blob/main/Go%E5%AE%89%E5%85%A8%E6%8C%87%E5%8D%97.md)
    - url: https://github.com/golangci/golangci-lint
      score: 5
      doc: https://golangci-lint.run/usage/configuration/
      record:
        - 【2025-08-02】之前写了“有时间花个整块时间彻底研究一下，看看是否可以通过golangci-lint解决《100 golang mistakes》里的这些问题。如果有的话之后这类内容就不用看了。”。具体可以查看【dotfiles/issues/11】。移除掉【100-go-mistakes】、【uber-go/guide】。有了这个以后就可以大幅度降低心智负担，总之肯定是更方便了。***这些东西本身更类似“良好的编程习惯”，本身也不需要记忆，现在只是做了这个工具来强制规范，以此来让自己养成良好习惯罢了。或者说，跟是否记忆没关系，更需要的是以此养成好习惯。***



    - url: https://github.com/sourcegraph/conc
      score: 5
      des: 相当于对sync的封装，可以用来写更简洁和标准的并发代码（增加代码可读性），并且可以有效避免很多“低级失误”（也就是他所说的 Make it harder to leak goroutines，比如各种 `defer <-done` 来关闭goroutine，防止leak）。比如说对wg和mutex的封装，以及用Catcher来封装recover逻辑，来统一处理panic
      record:
        - 【2025-03-01】用conc代替了之前这个rss2newsletter里面的 errgroup. 本身内置了并发控制、超时控制、错误收集。我之前这部分代码有个bug：偶发性的会整个hang住，执行不下去（导致github actions严重超时）。另外更好地实现了我的需求：收集所有错误feed `p := pool.NewWithResults[feeds.RssFeed]().WithContext(ctx).WithMaxGoroutines(10)`，创建一个结果池，用于收集处理结果。限制最大并发数。
        - 【2025-06-28】移除【fatih/semgroup】，只支持 并发数限制、错误传播机制。被conc完全上位替代了。
      qs:
        - 【基础并发控制】（增强版 WaitGroup）
        - 【并发数限制】（pool.Pool.WithMaxGoroutines()）
        - 【错误传播机制】（可配置首错/聚合错误，类似 errgroup）
        - 【上下文取消支持】（p.WithContext(ctx)，类似errgroup.WithContext）
        - 【Panic 安全处理】（自动捕获并转换 panic）
        - 【有序流处理】（stream.Stream）
        - 【并发切片处理】（iter.Map/ForEach）
        - 【结果收集】（resultpool 结果池）
        - Catcher对recover逻辑的封装
        - 提供了哪几种worker pool (ContextPool, ResultContextPool, ResultPool, ErrorPool)
        - stream （用来保证结果有序）
        - ForEach、map操作（用来处理slice）
      topics:
        - topic: 《conc深入使用记录》 # FIXME conc-usage
          slug: conc-usage
          qs:
            - conc 是否提供 ErrorGroup 、 SingleFlight 、 semaphore 、 cyclicbarrier 的写法

    #- 总结：Go 标准库有相关功能（如 errgroup 包实现 ErrorGroup ， golang.org/x/sync/singleflight 实现 SingleFlight ）， conc 可能提供类似实现，但具体取决于其设计，且标准库和 conc 实现可能在功能、接口上存在差异。
    #
    #核心就是对分组执行的任务的并发控制。可以看成是对 WaitGroup 的扩展。扩展的点主要是针对 WaitGroup 的不足之处:
    #
    #- 并发数没有控制，包括: go-waitgroup，SizedGroup，neilotoole/errgroup，Go 官方的 ErrGroup
    #- 无法收集子 goroutine 返回的错误，包括: facebookgo/errgroup，ErrSizedGroup
    #
    #而 Hunch、gollback、schedgroup 则是对分组任务执行语义上的扩展: 等待全部执行完成，等待某一个执行完成，定时执行。
    #
    #[fatih/semgroup: Like errgroup/waitgroup, but only runs a maximum of tasks at any time.](https://github.com/fatih/semgroup)
    #
    #:::info[REF]
    #
    #- [发现 conc 并发库一个有趣的问题](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453983&idx=1&sn=5bd0405cbcd56326a6801799e1a48af5)
    #- [聊聊并发库 conc | 董泽润的技术笔记](https://mytechshares.com/2023/01/28/talk-about-conc/)
    #- [Go 语言实现的可读性更高的并发神库](https://mp.weixin.qq.com/s?__biz=MzUxMDI4MDc1NA==&mid=2247498293&idx=2&sn=da1b66a36bc7286b9d75b29b2d77dc00#rd)
    #
    #:::
    #- name: semaphore
    #  des: semaphore 是用来控制多个 goroutine 同时访问多个资源的并发原语。信号量的 P/V 操作，P 操作是 descrease/wait/acquire，是减少信号量的计数值。而 V 操作是 increase/signal/release，是增加信号量的计数值。
    #- name: CyclicBarrier
    #  des: CyclicBarrier是一种同步原语，它允许一组线程在某个临界点处等待，直到所有线程都达到该点，然后同时继续执行。与Barrier不同的是，CyclicBarrier在每个线程到达临界点后会自动重置，可以重复使用。***用于重复进行一组 goroutine 同时执行的场景。***






    - url: https://github.com/mitchellh/mapstructure
      des: 【map和struct互相转化】map[string]interface{} to struct? How to convert []map to []struct? # convert map to struct. 如果 map 中没有该参数，需要保证数据库中该参数的默认值为 0，因为没有参数时 mapstructure 会赋值为 0，如果不为 0 就会出错；`mapstructure:",omitempty"` 如果为空就不展示。 [SliceToMapSlice struct切片转为map切片](

    - url: https://github.com/alphadose/haxmap
      des: 更好的并发hash。无锁+泛型支持。

    - url: https://github.com/darccio/mergo
      des: 【怎么合并两个struct，或者两个map?】 # merge structs and maps

    - url: https://github.com/jinzhu/copier
      des: 【怎么实现各种 struct 互相转换?】
      qs:
        - copier.Copy(to, from) 的两个参数，需要注意的`to`一定要有`from`的参数
        - 要标明 `copier:"must"` 的 tag，有三种 tag (must/nopanic/-)
      # 字段到字段的复制：根据字段名称进行匹配，实现从一个结构体到另一个结构体的字段复制。
      # 切片到切片的复制：支持从一个切片复制到另一个切片。
      # 结构体到切片的复制：可以将一个结构体复制到一个切片中。
      # 映射到映射的复制：支持从一个映射复制到另一个映射。
      # 字段操作：通过标签控制字段复制行为，例如强制复制、忽略字段、覆盖字段等。

      #- 调用同名方法为字段赋值
      #- 以源对象字段为参数调用目标对象的方法，从而为目标对象赋值（当然也可以做其它的任何事情）
      #- 将切片赋值给切片（可以是不同类型哦）
      #- 将结构体追加到切片中



    - url: https://github.com/stretchr/testify
      des: 拥有断言能力，一般采用 Table-Driven 方式编写测试用例，但这样用例之间的层级关系不够明显，并且和其他 mock/stub 框架结合使用的灵活程度不如 GoConvey。但是相比于 testing 的断言更多，用法更多样 (支持 assert 和 require 两种用法)
      #rep:
      #  - url: https://github.com/smartystreets/goconvey
      #    des: 能够方便清晰地体现和管理测试用例，断言能力丰富。而且层级嵌套用例的编写相较于 Table-Driven 的写法灵活轻量，并且和其他 Stub/Mock 框架的兼容性相比更好，不足之处在于理解起来可能需要一些学习成本。
      #  - url: https://github.com/cweill/gotests
      #    des: 一个自动为Go源代码生成测试代码的工具
      #
      #  - url: https://github.com/maxatome/go-testdeep
      #    des: Go语言的深度测试断言库，提供丰富的比较功能
      rel:
        - url: https://github.com/agiledragon/gomonkey
          des: 【函数/结构体打桩】Similar tools include mockery, testify-mock, gostub. But gomonkey not suppport interface, just support func and struct.
          doc: https://mp.weixin.qq.com/s?__biz=MzI4NDM0MzIyMg==&mid=2247490825&idx=1&sn=ea12de64c3aee953820174dc1e8fb1d4

        - url: https://github.com/go-testfixtures/testfixtures
          des: 用来模拟数据库进行测试的工具，把样本数据保存在fixtures文件中。在执行每个测试之前，测试数据库都会被清理并将fixture数据加载到数据库中。

        - url: https://github.com/xhd2015/xgo
          des: 非常好用的golang单测工具，xgo 是一个并发安全的 Mock 库，同时具备 Trace 收集的功能，能极大帮助开发者提升单测效率。xgo提供了一个全功能的Golang测试工具集，包括 Mock, Trace, Trap和增量覆盖率。
          doc: https://www.youtube.com/watch?v=PFU51LOaj-4

        - url: https://github.com/maxatome/go-testdeep
          des: ???
        - url: https://github.com/orlangure/gnomock
          des: 【管理测试中的 Docker 容器】移除【dockertest】，gnomock 相较 dockertest 更强，因为它内置 30+ 常用服务的自动化预设（gnomock 内置 PostgreSQL/Redis/Kafka 等 30+ 服务的预设配置）和一键健康检查（dockertest 需手写重试逻辑，gnomock 自动处理服务就绪），彻底消除手动配置模板代码。 # 【dockertest】具体来说，通常用来模拟数据库，但是与sqlmock或者redismock不同的是，dockertest需要启动docker容器，所以相应的，开销更高，也不易集成到CI中，但是可以模拟更真实的场景，使用数据库的所有feats，也能发现更多bug. dockertest vs testcontainers. [Go Integration Tests using Testcontainers : r/golang](https://www.reddit.com/r/golang/comments/x2yfqb/go_integration_tests_using_testcontainers/)

        - url: https://github.com/onsi/ginkgo
          des: ginkgo 是 BDD 开发模式工具，测试框架，没用
          sub:
            - url: https://github.com/onsi/gomega
              des: ginkgo以来的断言库，Gomega 提供了一套丰富且灵活的匹配器，允许开发者编写清晰且可读的测试断言，用于单元测试、集成测试和验收测试。

        - url: https://github.com/traefik/mocktail

        - url: https://github.com/uber-go/mock
          des: 【接口 Mock】gomock, mockgen, fork from golang/mock(EOL). ***使用 gomock 来做接口 mock，不要使用 testify-mock/mockery/gomonkey, 都不好用***. mock 不是用来处理简单的无状态函数，其应用场景主要在于处理不可控的第三方服务、数据库、磁盘读写等。如果这些服务的调用细节已经被封装到 interface 内部，调用方只看到了 interface 定义的一组方法，那么在测试中 mocks 就能控制第三方服务返回任意期望的结果，进而实现对调用方逻辑的全方位测试。

        - url: https://github.com/go-redis/redismock
          des: 怎么mock redis?
        - url: https://github.com/tokopedia/gripmock
          des: 怎么mock gRPC?
        - url: https://github.com/DATA-DOG/go-sqlmock # [Go单测从零到溜系列—2.mock数据库测试](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651450069&idx=3&sn=6acc4405f2af3a0f02c7b38bbb256702)
          des: go-mysql-driver mock?
          doc: https://piaohua.github.io/post/golang/20220813-go-sqlmock/

        - url: https://github.com/h2non/gock
          des: 怎么mock HTTP请求? # 用来模拟 HTTP 请求的工具，在我们写单元测试时，经常会遇到一些逻辑是需要请求别人的接口的场景，而我们无法完美模拟这些接口所需要的参数。这个时候就需要一个工具，当我们请求一些第三方接口时拦截请求，并返回我们的预设的返回值.比 gavv/httpexpect  和  jarcoal/httpmock 好用很多，推荐使用.


        - url: https://github.com/brianvoe/gofakeit
          des: golang test中怎么生成fake data? 快速生成假数据的 Go 库。该项目是用于生成各种假数据的 Go 语言库，支持随机生成名字、地址、电话和日期等格式的数据。它默认不支持生成中文假数据，但可以通过自定义方式扩展或调整生成逻辑实现。核心优势：持续维护 + 完整中文支持 + 函数级扩展能力 + 200+数据类型覆盖。 # 随机数据生成器。目前，它提供了160多个函数，涵盖少数不同的主题/类别，如Person、 Animals、 Address、 Games、 Cars、 Beers等等。这里的关键词是随机。
          record:
            - 【2025-07-03】【go-faker/faker】：核心缺陷是中文支持缺失且两年未更新，无法满足本地化需求。【toddlerya/fakerfactory】：虽然深度本地化，但绑定中文场景不可扩展，缺乏多语言能力。【Pallinder/go-randomdata】、【malisit/kolpa】已经EOL了。

        - url: https://github.com/josharian/impl
          des: impl generates method stubs for implementing an interface.
      topics:
        - topic: golang test
          qs:
            - testdata(.input, .golden), table-driven, Tags
            - 四种测试函数？注意事项？
            - 如何重复执行 gotest？怎么重复执行某个或者某组测试用例？

        - topic: x
          qs:
            - 为啥“在深度使用上述几个测试框架之后，个人感觉gomonkey+goconvery组合是比较合适的” # [选择合适的测试框架 - MySpace](https://www.hitzhangjie.pro/blog/2020-08-19-go%E5%BC%80%E5%8F%91%E5%A6%82%E4%BD%95%E5%81%9A%E6%B5%8B%E8%AF%95%E9%80%89%E6%8B%A9%E5%90%88%E9%80%82%E7%9A%84%E6%B5%8B%E8%AF%95%E6%A1%86%E6%9E%B6/)

        # [go benchmark实践与原理 - 木白的技术私厨](https://cbsheng.github.io/posts/go_benchmark%E5%AE%9E%E8%B7%B5%E4%B8%8E%E5%8E%9F%E7%90%86/)
        - topic: golang benchmark test
          url: https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453468&idx=1&sn=85d78f7ccc3c22ffb12e4a81e8bd3877
          qs:
            - benchmark, metrics
            - benchmark 有哪些注意事项？b.ResetTimer(), b.StopTimer(), b.StartTimer()
            - 有哪些常用方法？ # b.ReportAllocs(), b.SetBytes()
            - benchmark 的返回参数都是啥意思？怎么分析？

            - 如何重复执行 gotest？怎么重复执行某个或者某组测试用例？ # Don't use build tags for integration tests. use env replaced with build tags in golang test 不够显式
            - go test 如何禁用缓存？ # 使用 -count=1 flag 禁用，不要GOCACHE=off，否则会影响 go.mod

        - topic: testify
          qs:
            - Which unittest lib should we use? test/testing vs testify vs goconvey # [Testify vs go test/testing comparison of testing frameworks](https://knapsackpro.com/testing_frameworks/difference_between/go-testify/vs/testing) [Replace `testify` usage by `gotest.tools` · Issue #12332 · cosmos/cosmos-sdk](https://github.com/cosmos/cosmos-sdk/issues/12332) 如果需要测试辅助函数 (无状态函数), 就用`testify`进行`table driven`测试。如果需要测试业务逻辑，就用`goconvey`, 更有层次感。
            - 使用 require 失败后，所有测试都会停止

    - url: https://github.com/panjf2000/ants
      des: chan+wg, ants # [Ants源码分析](https://tech.qimao.com/ants-yuan-ma-fen-xi/)
      record:
        - 【2025-06-29】移除掉【tunny】。 # 协程池, 来控制协程的并发数量. Better than Jeffail/tunny, oxtoacart/bpool, valyala/bytebufferpool. tunny设计的思路与ants有较大的区别。（1.）tunny只支持同步的方式执行任务，虽然任务在另一个 goroutine 执行，但是提交任务的 goroutine 必须等待结果返回或超时。不能做其他事情。正是由于这一点，导致tunny的设计稍微一点复杂，而且为了支持超时和取消，设计了多个通道用于和执行任务的 goroutine 通信。一次任务执行的过程涉及多次通信，性能是有损失的。从另一方面说，同步的编程方式更符合人类的直觉。（2.）ants完全是异步的任务执行流程，相比tunny性能是稍高一些的。但是也因为它的异步特性，导致没有任务超时、取消这些机制。而且如果需要收集结果，必须要自己编写额外的代码。 # TODO 之后再研究一下这玩意。
    #1. **性能与资源效率**
    #- **`ants` 最优**：通过协程复用和自动伸缩，内存占用仅为原生方案的 10–20%，吞吐量提升 2–6 倍
    #- **`tunny` 适用特定场景**：同步模型适合需要严格控制任务生命周期的场景，但通信开销限制了高并发性能
    #2. **任务控制能力**
    #- **`tunny` 更全面**：内置超时和取消机制，通过 `ProcessCtx` 支持上下文中断
    #- **`ants` 依赖外部实现**：需结合 `context` 或自定义超时逻辑
    #3. **开发体验**
    #- **原生 Goroutine 最简**：无学习成本，适合简单并发。
    #- **`ants` 平衡效率与复杂度**：异步模型减少回调嵌套，但需管理任务提交和资源释放
    #- **`tunny` 编程负担较重**：同步阻塞需处理多 Goroutine 协作，易引发死锁
    #### **选型建议**
    #- **优先 `ants`**：适用于 Web 服务、爬虫、批量任务等高并发场景，以资源复用和自动伸缩最大化性能
    #- **考虑 `tunny`**：当任务需严格超时控制或取消逻辑（如分布式事务、实时响应系统）时选用
    #- **原生 Goroutine**：仅用于低频、短时任务，且需显式限制并发数（如 `semaphore.Weighted`）避免 OOM



    - url: https://github.com/uber-go/automaxprocs
      des: 怎么自动设置 golang 的 GOMAXPROCS 来最大化利用 CPU?
    - url: https://github.com/KimMachineGun/automemlimit
      des: 怎么自动设置 golang 的 GOMEMLIMIT 来最大化利用内存? # 类似 GOMAXPROCS 之于 CPU，但是 automemlimit 是用来限制内存的，通过自动设置 golang 的 GOMEMLIMIT 环境变量，以便与 Linux cgroups 的内存限制相匹配。它的主要目的是帮助限制 Go 程序的内存使用，确保程序不会超出在 cgroups 中设置的内存限制。

    - url: https://github.com/spf13/cast
      des: 怎么修改变量类型? # switch...case... + string(xxx) will cause data copy, bad performance

    - url: https://github.com/samber/lo
      des: Pretty useful. golang utils for map and slice
      topics:
        - topic: lo
          htu:
            - lo.Uniq(), lo.Filter(), lo.Map()
            - 【怎么对slice进行去重?】lo.FindUniques() # [lo/find.go at master · samber/lo](https://github.com/samber/lo/blob/master/find.go#L112)



    - url: https://github.com/gookit/validate
      des: Go通用的数据验证与过滤库，使用简单，内置大部分常用验证、过滤器，支持自定义验证器、自定义消息、字段翻译。这个是比较类似laravel/tp提供的那种validation，用起来很舒服。maintainer维护的还可以。
      rel:
        - url: https://github.com/go-playground/validator
          des: filed-validator. go-playground/validator 没有内置支持regex，需要添加CustomRegex才能实现. validator的优势是内置了很多常用rules，开箱即用，比如ip验证、string验证、format验证、image验证等（README里有写），但是没有内置支持regex。缺点：除了基于tag实现validate都存在的问题以外（侵入性强不灵活），validator需要使用translator才能实现 自定义错误消息（也就是中文提示），相比之下很麻烦。 # [Golang参数校验：go-playground/validator的缺点及替代品checkerGolang的参数校验， - 掘金](https://juejin.cn/post/6905661995379326989) 缺点罗列 [Go 验证器 validator- 掘金](https://juejin.cn/post/7250044327883128891) 比较好的一篇validator文档
        - url: https://github.com/bytedance/go-tagexpr
          des: go-tagexpr 则更灵活，可以定义更复杂的rules. 但是没有什么内置rules. # [[Bug] 校验正则表达式校验的结果与预期不符 · Issue #82 · bytedance/go-tagexpr](https://github.com/bytedance/go-tagexpr/issues/82)
        - url: https://github.com/RussellLuo/validating




    - url: https://github.com/gookit/goutil
      des: 常用dump.Print()来打印数据，很好用的golang工具函数包

    - url: https://github.com/google/go-cmp
      des: 怎么比较var? 怎么判断var是否相等? # Used to compare values in golang. If we use reflect.DeepEqual to compare, unexported fields are not compared by default.

    - url: https://github.com/samber/mo
      score: 5
      des: monads and popular FP abstractions
      rel:
        - url: https://github.com/IBM/fp-go


    - url: https://github.com/shopspring/decimal
      doc: https://mp.weixin.qq.com/s?__biz=MzkyMDAzNjQxMg==&mid=2247484440&idx=1&sn=ed2e6bc81a6b40bf8bd1c2d6cf31d0af
      des: 怎么解决golang的精度问题? 挺好用的，相当于math库。longzhu项目里用来处理float的精度问题（struct里定义为float64，然后在具体计算时的精度问题用这个pkg处理）。float和int都有坑，可以用这个pkg避坑。

    - url: https://github.com/dromara/carbon
      score: 5
      des: 轻量级的、易于使用的、语义智能的日期时间库。基本上所有时间相关的操作，都能满足，非常好用。
      record:
        - 【2025-06-24】carbon可以完美覆盖【jinzhu/now（用StartOfDay()之类的）】、【mergestat/timediff（用DiffForHumans()）】。【rickar/cal】无法覆盖，但是该repo不支持中国假期。所以这三个都移除掉。


    - url: https://github.com/google/wire
      doc: https://lailin.xyz/post/go-training-week4-wire.html
      score: 2
      des: DI. wire比【uber-go/fx】、【samber/do】更好用。Wire 在编译期生成显式依赖代码，消除运行时反射风险，让错误检查提前到编译阶段，大幅提升大型项目的可维护性和可靠性。 dig 是通过运行时反射实现的依赖注入。而 wire 是根据自定义的代码，通过命令，生成相应的依赖注入代码，在编译期就完成依赖注入，无需反射机制。这样的好处是：首先，方便排查，如果存在依赖错误，编译时就能发现。而 dig 只能在运行时才能发现依赖错误。其次，避免依赖膨胀，wire 生成的代码只包含被依赖的，而 dig 可能会存在好多无用依赖。依赖关系静态存在源码，便于工具分析。
      topics:
        - topic: DI基本认知
          qs:
            - golang项目中是否真的有必要用DI? 为啥还是更建议使用DI呢? # 如果不用 DI 主要有两大不方便的地方，一个是底层类的修改需要修改上层类，在大型软件开发过程中基类是很多的，一条链路改下来动辄要修改几十个文件；另一方面就是就是层与层之间单元测试不太方便。
            - 为啥还是更推荐使用wire呢? wire 相比 dig和fx有啥优缺点? # [golang 依赖注入 wire 和 dig 体验对比](https://jerryzhou343.github.io/post/di/) [Go 依赖注入：为什么把 dig 迁移到 wire](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651448749&idx=3&sn=784606f173c679446999c5092b8e1973)



    - url: https://github.com/parca-dev/parca
      des: 非常好用，Parca 是一个利用 eBPF 技术进行高效、低开销的性能分析工具，同时它与 pprof 格式兼容，使得它可以轻松集成到现有的性能分析工作流程中。并且相比于pprof等工具，parca 可以自动发现运行在 k8s 或 systemd 环境中的所有目标，也就是说parca是非侵入式的。并且内置了Web UI以及storage机制。metrics和pprof相差不大，也都是CPU, memory, IO之类的。
      record:
        - 【2025-07-03】最终选择【Parca】，核心理由是：它通过 eBPF 技术实现零侵入、全自动的持续性能分析，无需代码修改即可监控整个基础设施（如 Kubernetes 或 systemd 环境），且性能开销极低（可忽略不计）还内置存储和 Web UI，兼容 pprof 数据，直接覆盖生产环境监控需求，避免工具链碎片化，是最简且最强大的统一解决方案。。移除【Pyroscope】，虽支持自动采样但需代码集成。移除【goref】，专注内存泄漏但需加载 Core 文件。移除【statsviz】，持续采样导致高开销。移除【pprof】，只能手动触发，不提也罢。
        - 【2025-07-03】parca和pprof是协同关系，Parca不支持以下需求： 1、无代码行级分析（Parca 火焰图只显示到函数级别）。2、无交互式调试（无法实时执行 list/traces 等命令）。3、历史对比不够灵活（pprof CLI 支持任意时间点 profile 对比）。这些都需要pprof导入到本地之后查看。



    # TODO [2025-06-28] Compare event-driven network libs, twisted(python), golang(gnet, cloudwego/netpoll, evio), java(netty, guava, akka) 有时间研究一下
    # [gnet-io/gnet-examples: Examples of gnet](https://github.com/gnet-io/gnet-examples)
    - url: https://github.com/panjf2000/gnet
      des: 高性能、轻量级、非阻塞的事件驱动 Go 网络框架


    # [imsilence/htgorm](https://github.com/imsilence/htgorm) 一些gorm使用示例
    - url: https://github.com/go-gorm/gorm
      doc: https://gorm.io/zh_CN/docs/index.html
      des: orm, better than xorm or sqlx. support for mysql, pgsql, sql server, TiDB, clickhouse and sqlite.
      sub:
        - url: https://github.com/go-gorm/dbresolver
          des: DBResolver 读写分离. 类似vitess
        - url: https://github.com/go-gorm/optimisticlock
          doc: https://chedan.io/gorm-optimistic-lock
          des: 如何在GORM中使用乐观锁?
      record:
        - 【2025-06-28】移除掉了 2个ORM（【ent】、【bun】、【gorose】）、2个编译型sql工具（【sqlc】、【go-jet/jet】）、【sqlx】、2个DB驱动层（【go-mysql-server】，这种DB驱动层可以单独使用（也就是直接写sql），也可以搭配gorm这种ORM，gorm会在上层封装更多高级功能（比如query builder, trx之类的））、sql拓展工具【sqlx】 # gorm和sqlx. [我在阅读一篇文章后知道，sqlx在性能上高于gorm，那使用gorm仅仅是为了避免sql注入嘛 · Issue #6689 · go-gorm/gorm](https://github.com/go-gorm/gorm/issues/6689)
      topics:
        - topic: gorm
          htu:
            - gorm 多对多关系的多表联查怎么搞？
            - gorm 读写分离 DBResolver
            - gorm配置项 maxopen、maxidle、maxlifetime 分别是啥？为啥生产环境一定要配置这三个配置项？
            - gorm upsert, hints
            - gorm中怎么使用CTE呢?
            - gorm怎么避免深分页？ # 当使用GORM时，务必开启gorm.Config{PrepareStmt: true}启用预处理语句缓存，可提升30%查询性能。对分页查询场景，优先使用db.Scopes(Paginate(page))而非Offset，避免深度分页慢查询。
            - 【乐观锁】怎么在GORM中使用乐观锁？
          hto: # [Gorm使用心得，那些你可能会踩到的坑。 - 知乎](https://zhuanlan.zhihu.com/p/304213716)
            - 结构体查询时int类型的0值问题
            - 防止sql注入
            - gorm 的 Updates 有个坑啊 —— 如果你传的是一个 struct，gorm 默认是不更新 “零值” 字段的。比如 `tx.Table(p.tableName).Where("id=?", configID).Updates(t).Error` 这样的代码。我们要通过在使用gorm更新配置时，如何确保零值字段也能被正确更新呢？ # gorm的update(struct)确实不会更新其中的零值字段。为了避免这个问题，有两种方法，在update中指定要更新的字段，或者新加个select来指定要更新的字段
            - gorm 的 update的坑？ 如果参数类型为struct，则gorm默认不更新struct里的“零值字段”

            - 需要使用数组时
            - 需要json绑定数据时
            - 自生成id与时间戳

            - golang 里 tinyint(1) 和 bool 的问题？ 建议用 tinyint(3)，status 两种，1 和 2
            - 结构体查询时int类型的0值问题？ 当进行整个结构体查询时 gorm会默认把没有设置的值为0 所以无法进行查询 gorm只能进行非零字段查询。（不推荐结构体查询）目前的解决方法是： 当要进行结构体查询时将结构体中的int改为指针int类型
            - gorm哪些查询语法无法避免sql注入？
            - 自动生成uuid或者时间戳？ gorm:"type:uuid;primaryKey;default:gen_random_uuid()" 这种tag只有 DB.Create(&agent) 才能自动生成，直接insert不支持
            - 为什么国内的微服务框架都不用 gorm 作为微服务框架的 orm？ # [为什么国内的微服务框架都不用 gorm 作为微服务框架的 orm？ - V2EX](https://www.v2ex.com/t/1095255)



    - url: https://github.com/gin-gonic/gin
      record:
        - 【2025-07-03】把之前整理的【go-chi/chi】、【Go-Spring】、【Beego】、【goravel】、【echo】、【iris】、【fiber】。比较维度是：技术核心、路由算法、中间件原理、核心特点、独特功能。没啥好对比的，路由算法全都是 Radix树(LCP优化)，中间件原理也都差不多。
      topics:
        - topic: web框架(gin)
          des: "***技术选型&核心需求：从（路由算法（LCP using Trie-Tree）、生命周期、中间件原理）比较几种golang web 框架（gin, iris, ）？***"
          hti:
            # [「Go框架」深入理解iris框架的路由底层结构](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453986&idx=1&sn=4ef797a87fc303e578a771c44bd256b2)
            - CTT（压缩前缀树）, 在TrieTree的基础上，通过合并唯一子树预期父节点来节约空间

            - Gin框架中如何处理HTTP请求 # [通过分析gin、beego源码，读懂web框架对http请求处理流程的本质](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453917&idx=1&sn=334020f003deec68f268d1c36143ddca) [Go BIO/NIO探讨(1)：Gin框架中如何处理HTTP请求](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453950&idx=1&sn=1cb70938803a6c23bdfc957a0e5ff385)

            - gin.HandlerFunc 相较于 golang官方的 http.HandlerFunc 有啥，为啥不（像gozero之类的一样）直接用这个？
          htu:
            - 把路由分模块管理（也就是直接把路由拆分到不同文件） # [Go项目实战-API路由的分模块管理](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247502227&idx=1&sn=b9b62cd73f9a5629c2169c6639c4fcd5)
            - 更好的api响应 # [Go API 多种响应的规范化处理和简化策略](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247502082&idx=1&sn=ee4a668b30c09bdbefd25993dfb80ae6)


    - url: https://github.com/gogf/gf
      des: goframe # garray.NewStrArrayFrom().Unique().Join() 的写法非常优雅

    - url: https://github.com/julienschmidt/httprouter
      des: ???

    - url: https://github.com/go-resty/resty
      record:
        - 【2025-07-03】移除【dghubble/sling】，相比之下，resty在绝大多数项目（尤其是生产环境）中碾压式胜出，它提供了开箱即用的生产必备功能（重试、调试、中间件、认证、超时控制/连接池、Cookie 管理、SSE 等），以及显著提升开发效率和体验（链式API、减少样板代码）。也移除【hashicorp/go-retryablehttp】，这个核心功能是为了支持重试机制（自动重试、backoff），resty也能替代。

    - url: https://github.com/avast/retry-go
      des: # 需要在log中打印当前重试次数，怎么获取当前attempts # 通过 retry.OnRetry(func) 可以获得当前重试次数，且支持backoff, random 等重试机制
      record: 【2025-07-03】移除【sethvargo/go-retry】，相比之下还需要自己添加backoff之类的重试机制，用起来有点麻烦，但是性能会稍微好点。

    - url: https://github.com/uber-go/zap
      des: 【golang logging】
      record:
        - 【2024-9-15】不要使用 logrus，因为 logrus 使用了大量反射，导致大量内存分配。golang logging真正有必须要学的只有zap和内置的slog，一个重量一个轻量，并且都功能强大。***推荐使用 zap 或者 zerolog，这两种是没有使用反射，没有内存分配。还都支持 取样器 Sampler 来限制每秒写入的日志数量，以减少性能开销。也都支持zero allocs.***
        - 【2025-08-07】移除“【技术选型】logging”，排除掉【logrus】，其实就3个【zap】、【slog】、【zerolog】，其实这三个没啥区别（在），这里有个常见误区，之所以使用reflect，因为在处理 zap.Any(), slog.Any() 之类的数据时，如果前面的类型推导无法获取其数据类型，就需要用reflect做兜底。实际上zap和zerolog也用反射了。所以这里更多的区别还是功能支持，日志轮转。所以你是否需要这些功能？以及你是否只想用std，不想使用第三方pkg？通常来说，如果需要日志轮转就zap，如果只想用std就slog。【2025-08-11】太折腾了，我已经懒得弄清楚这三个pkg之间的区别了。真想弄明白只能看源码。写了一个benchmark，总的来说不出所料，性能排序如下：zerolog > zap > slog
      qs:
        - 日志降级是啥？怎么实现？
        #  将日志会拆分成info，err两种日志文件，可以对info日志很好的降级，但err日志不会受任何影响，能够更好的去监控我们服务的错误问题。
        #
        #  首先，我们将日志分为三种类型，access请求类型，worker任务类型，biz业务类型。基础框架处理access和worker类型日志，用户业务处理biz类型日志。
        #
        #  其次，我们的日志错误级别包含5中类型panic，error，warn，info，debug。panic、error日志均做了统一的告警处理。我们的debug日志结合配置中心的动态配置，可以很方便我们排查线上问题。并且日志会根据级别拆分成info，err两种日志文件（参考ngx的日志）。这样的好处就是我们可以对info日志很好的降级，但err日志不会受任何影响，能够更好的去监控我们服务的错误问题。
        #
        #  最后，我们将日志分层成框架日志和业务日志，主要是为了做日志的采集分流，同时框架集成统一的access、worker日志可以方便我们进行统一监控告警、统一业务报表。

        - "***access log 包括哪些字段（lv(info, err, warn, ...), ts, msg, aid, iid, host, cost）***"
        #  req
        #  - req.aid
        #  - req.method
        #  - req.path
        #  - req.len
        #  - req.cip
        #
        #  rep
        #  - rep.len
        #  - rep.code
        #  - rep.err

        - "***logging的前端和后端分别是啥？***"
        #  总结来说，前端日志管理侧重于收集和初步处理用户端的日志数据，而后端日志管理则负责更深层次的日志分析和存储。两者共同构成了一个完整的日志管理系统，帮助开发者和系统管理员监控和分析应用的运行状态。
        #  前端日志管理
        #  前端日志管理主要涉及到用户界面和用户体验相关的日志收集。这些日志可能包括用户行为数据、页面加载时间、错误和异常信息等。前端日志管理的关键点包括：
        #  日志收集：在用户端脚本执行过程中产生的日志会落地到本地的存储容器中。
        #  日志上报：当遇到用户反馈或者端上异常被捕获时，会触发本地日志的上报。
        #  用户端SDK：负责存储与上报端上日志，如美团开源的Logan Web项目，它在前端实现日志存储及上报。
        #  数据安全：在本地已存储的日志需要有数据安全保障。
        #  后端日志管理
        #  后端日志管理则涉及到服务器端的日志处理，包括日志的接收、解析、整合与分析。后端日志管理的关键点包括：
        #  服务器端：负责接收、解析、整合与分析日志。
        #  日志分析平台：提供日志的查询与数据可视化展示。
        #  数据存储：后端可以选择不同的存储系统，如ElasticSearch, H2 或 MySQL集群等，用于存储日志数据。
        #  日志框架：使用如Log4j这样的框架来控制日志信息输出到控制台、文件、甚至是数据库中，并定义日志的输出格式和级别。

      topics:
        - topic: zap
          what:
            - "***选择logging pkg的核心需求?（自定义字段（json 格式（自定义格式化日志和输出））和日志级别、hooks、线程安全、性能要好、其他特性（旋转日志、自动分割日志、支持修改时间格式））***"
          htu:
            - zap怎么同时输出到文件、console和kafka? 并且以不同格式（比如Text和JSON）
            - logging的前端和后端分别是啥？
            - 怎么分割logging
            - 怎么实现不同service把日志打到
            - sugared和logger除了perf（通过减少使用interface和reflect）还有啥区别吗? 原生支持格式化
            - 怎么把zap集成到gin中？
            - SugaredLogger, format, with(kv)
            - NewExample()/NewDevelopment()/NewProduction()这 3 个函数可以传入若干类型为zap.Option的选项，从而定制Logger的行为
            - zap.NewDevelopment() 默认console. zap.NewProduction() 默认json.



    #- 推荐使用 json 格式日志，便于各种后续的数据处理。
    #- 写好“默认字段”（如 IP、uid、运行环境等参数）的公共方法，便于调用
    #- logrus 怎么纪录 function 名称？使用`log.SetReportCaller(true)`
    #- 怎么在输入到日志的同时，在控制台打印出来？
    #- logrus 的 fatal 输出之后，还有一些逻辑要处理怎么办？用`RegisterExitHandler`在 fatal 前处理一些问题
    #- 怎么单独实例化一个 logrus，全局使用？
    #- logrus 有哪些常用 hook？ *hook 就相当于第三方插件，用来实现一些附加功能（最常用的就是日志的 rotate 了*）。logrus 设置 hook 后，每条日志输出前都会执行该 hook。logrus 内置了 syslog 钩子（其他还有 mogrus logrus-redis-hook logrus-amqp 分别用来把 logrus 发送到 mongo、redis 和 MQ 中） [logrus/hooks](https://github.com/sirupsen/logrus/tree/master/hooks)
    #- logrus 有哪些“第三方日志格式”？比较常用的是 logstash 和`nested-logrus-formatter`
    #- 另外，logrus 默认使用 mutex 保护并发写操作，如果确定程序是线程安全的，可以使用`logger.SetNoLock()`来禁用 mutex（比如未注册 hook，或者 hook 是线程安全的；向 logger.Out 写入日志是线程安全的）




    - url: https://github.com/bytedance/sonic
      des: used to replace encoding/json, convert JSON to map or struct. faster than jsoniter, easyjson and go-json. easyjson need to generate file, jsoniter 比 gabs 好用.
      record:
        - |
          【2025-08-09】移除“【技术选型】golang json库”，用EQC进行归类（性能-兼容性-内存占用/易用性）。可以看到非常明确的对应关系。
          - 【EQ（代码生成型）】（easyjson）使用麻烦，需要预先生成代码。
          - 【EC（查询解析型）】都是 查询解析型 的（无非是 gjson是惰性解析，fastjson是手动解析，jsonparser是流式解析）。优势在于性能快、内存占用低。
          - 【QC（标准兼容型）】（encoding/json, go-json, jsoniter）牺牲了性能（普遍只有3x标准库的性能），并且都用了反射来保证易用性，导致内存占用并不低。所以这里可以看到也只是一半的C（保证易用性，牺牲内存占用）。
          - 【平衡型（SIMD加速型）】（sonic）除了无法标准兼容encoding/json的话（只是部分兼容），在EQC上相对平衡。这也是当前选择sonic的理由。其通过JIT动态编译和SIMD硬件加速，在标准兼容型库中撕开性能突破口，成为高吞吐场景（如10万QPS）的最优解。若业务能接受边缘类型兼容性风险（如自定义MarshalJSON方法），它是当前技术条件下的理想选择。
      qs:
        - sonic config 的 EscapeHTML, SortMapKeys, CompactMarshaler, CopyString, ValidateString 分别有啥用?


    - url: https://github.com/go-nunu/nunu
      doc: https://go-nunu.github.io/nunu/guide
      des: 类似buffalo。使用gf还是nunu，或者说使用，这是个问题。在我看来，本质上来说，gf是个框架，而nunu是个starter。从这点上来说，gf自己实现了各种pkg，比如ORM、日志、错误处理、数据校验等等，而nunu则是组装了golang生态下的各种主流组件。并且二者都是适用于单体服务，而不是native微服务的。总之，二者的定位是类似的，gf选择自己实现pkg，那么带来的问题就是，组件质量是大概率不如nunu选择的主流开源组件，但是相应的，gf的集成性（比如说不存在nunu这种组装组件带来的兼容性问题）更好。，更倾向于选择哪种就看自己的偏好了
      record:
        - 【2025-06-25】移除【buffalo（类似laravel那样的starter（也就是用cli生成项目结构，并且内置了middleware, error handle等feat））】、【Melkeydev/go-blueprint（类似nunu这样，用来生成golang项目layout的cli工具（相比nunu定制性更强 1、支持多种流行框架（如 Gin、Fiber 等）2、可选高级功能，如 CI/CD 工作流程、Websocket 和 Docker 配置））】、【rk-boot（其实是个Generator，用单个yaml管理一些web相关的一些常用服务（其实还是framework+db+cache+middleware这些））】
        - 【2025-06-25】移除【go-clean-template】、【project-layout】、【go-backend-clean-architecture】这几个介绍golang项目标准模板的repo。无非是 (internal, pkg, cmd, api) 这套，没啥意思。

    - url: https://github.com/bytedance/godlp
      des: 身份证号码、地址之类的脱敏pkg，不是cli, 只能作为pkg在golang中引入，字节的实现

    - url: https://github.com/jpillora/ipfilter
      des: IP 过滤，有针对国家、单个 IP、IP 段等各种过滤模式，还可以作为中间件使用
    - url: https://github.com/coreos/go-iptables
      des: operates iptables in golang. Cuz we need to update iptables rules dynamically, so we need some libs like go-iptables. Actually, through why kube-proxy does not use iptables libs, we can see why iptables-pkg is not used much in actual scenarios. It is not as good as using the iptables command directly in terms of ease of use, performance, maintainability, cross-platform, etc. (kube-proxy why not use iptables libs like go-iptables to modify the rules? If we directly use the iptables commands to modify it, is it possible to ensure reliability?)

    - url: https://github.com/tinygo-org/tinygo
      des: wasm in golang. golang 的标准库支持编译成 wasm 文件，但是基本已经停止维护了。所以，需要使用 tinygo 才能更好地用 golang 开发 wasm。需要使用 wasmer-go 才能在 golang 调用 .wasm 代码。wasm 的主要应用场景，就是各种 cloud native 平台，各种 serverless 服务，比如 Envoy、Istio 等组件的二次开发，比如可以用 wasm 把自定义 filter 集成到 Envoy，实现 Envoy 代理的功能增强。

    - url: https://github.com/gookit/validate
      des: Go通用的数据验证与过滤库，使用简单，内置大部分常用验证、过滤器，支持自定义验证器、自定义消息、字段翻译。这个是比较类似laravel/tp提供的那种validation，用起来很舒服。maintainer维护的还可以。相比之下最大的好处在于更方便的自定义错误消息。
      record:
        - 【2025-06-28】移除掉【go-playground/validator（没有内置支持regex，需要添加CustomRegex才能实现. validator的优势是内置了很多常用rules，开箱即用，比如ip验证、string验证、format验证、image验证等（README里有写），但是没有内置支持regex。缺点：除了基于tag实现validate都存在的问题以外（侵入性强不灵活），validator需要使用translator才能实现 自定义错误消息（也就是中文提示），相比之下很麻烦。）】、【bytedance/go-tagexpr（go-tagexpr 则更灵活，可以定义更复杂的rules. 但是没有什么内置rules。不够开箱即用。）】、【asaskevich/govalidator（已经EOL了）】、【RussellLuo/validating】

    - url: https://github.com/go-co-op/gocron
      des: 【robfig/cron】已经EOL了，所以推荐这个

    - url: https://github.com/authelia/authelia
      des: 用来提供2FA认证和passkey的，类似gh现在的这样。可以作为pkg被使用，也可以直接集成到caddy, traefik之类的服务里，提供服务。

    - url: https://github.com/montanaflynn/stats
      des: math 库，提供了各种中位数、平均值之类 golang 没有官方那个 pkg 的东西

    - url: https://github.com/nicksnyder/go-i18n
      doc: https://xuanwo.io/2019/12/11/golang-i18n/
      des: golang生态下实现i18n

    - url: https://github.com/hairyhenderson/gomplate
      des: 一个功能强大的 go 语言模板库。真心比golang template好用。

    - url: https://github.com/petermattis/goid
      des: 怎么获取当前goroutine的ID?

    - url: https://github.com/golang-jwt/jwt
      des: Go语言实现的JSON Web Token（JWT）库，用于生成和验证JWT令牌

    - url: https://github.com/go-echarts/go-echarts
      score: 1
      des: Go语言的Echarts图表库，用于生成交互式数据可视化图表。
      record:
        - 【2025-06-24】可能会有疑问，这玩意有啥用？用 echarts 肯定是用ts写啊，不至于用golang写。但是这只是一般场景，是否会有跑批之后，需要生成一份包含图表的日志发邮件给相关人员的需求？如果是传统的前后端不分离，是不是要用这个直接处理好HTML给前端？


    - url: https://github.com/mingrammer/commonregex # [gotosocial/internal/regexes/regexes.go at main · superseriousbusiness/gotosocial](https://github.com/superseriousbusiness/gotosocial/blob/main/internal/regexes/regexes.go)
      des: 提供了一些常用regex，比如 手机号、URL、mail、IP、价格、时间、信用卡、md5、sha1等等

    - url: https://github.com/mvdan/xurls
      des: xurls用来提取text中的url，提供了Relaxed和Strict两种mode，后者无法提取没有schema的url，前者可以。

    - url: https://github.com/kubernetes-sigs/yaml
      record:
        - 【2025-01-05】【移除docs-alfred里的JSON render功能的相关代码】我在docs-alfred里用到了，之前是同时实现了YAML和JSON的render。后来想了下其实没必要，只实现一个YAML，然后用这个pkg提供的 YAMLToJSON() 直接转JSON就可以了。
        - 【2025-08-07】移除“技术选型【YAML库】”，也推翻了之前的一个认知。之前会以为【kubernetes-sigs/yaml】相比【goccy/go-yaml】更好用。刚刚又觉得后者更好用。实际上二者的指向都是很清晰的，两个问题：1、是否需要处理YAML特性？2、是否需要兼容JSON stdlib(encoding/json)？如果需要处理YAML特性，那就ggy，反之则ksy；如果需要兼容JSON，那就ksy，反之则ggy。具体来说，3个例子：1、之所以 k8s CRD开发选择ksy，归根到底也是因为这些YAML本身也没有使用YAML特性，且有强制兼容JSON的需求。2、我的docs-alfred有JSON和YAML互转的需求，并且我的YAML文件确实没有使用YAML高级特性，所以我选择ksy。3、用ggy来解析Docker-Compose.yml，因为其经常使用anchor, alias, 合并key, 注释 等高级功能。之所以如此，是因为ksy的why（为什么会有这个pkg）是用来解决k8s生态中YAML和JSON tag的兼容性问题，所以ksy的处理机制是：YAML->JSON->golang对象。而ggy的why就是用来处理YAML文件的（转化为JSON格式则是其附加功能），所以其工作机制就是解析YAML AST，处理JSON时则是尝试模拟encoding/json默认行为，所以不保证完全一致（部分JSON tag会被忽略（string, omitempty, inline））
      qs:
        - 【保证map的顺序】MapSlice // MapItem # Table []yaml.MapSlice `yaml:"table,omitempty" json:"table,omitempty"` 所有主流YAML库之类的基本上都支持该操作
      topics:
        - topic: YAML syntax
          url: https://github.com/yaml/yaml-spec
          why:
            - 【为啥需要YAML】怎么理解“yaml是json的超集”？可读性更好的YAML，并且【支持复杂结构】通过锚点、多文档等特性实现配置复用和模块化，解决大型项目配置冗余问题
          what:
            - 【核心定义】YAML Ain't Markup Language：非标记性数据序列化语言，支持标量/列表/字典三种基础结构
            - 【技术特性】包含锚点(&)、别名(*)、多文档(---)、类型标签(!!)等高级语法
          ww:
            - 【配置管理场景】Kubernetes/Ansible/Docker Compose等DevOps工具首选配置格式
            - 【数据交换场景】API设计/自动化测试（如KDT关键字驱动）
            - 【限制条件】缩进敏感（必须空格）、布尔值歧义（true/True/TRUE）、复杂嵌套降低可读性
          htu:
            - 【锚点复用】用`&锚点`定义变量，`*别名`引用，`<<:`合并映射：
            - "*yaml的key的value怎么复用? Anchors and aliases*" # [YAML anchors | Bitbucket Cloud | Atlassian Support](https://support.atlassian.com/bitbucket-cloud/docs/yaml-anchors/)
            - yaml数据类型强制转换
            - yaml多文件怎么parse
            - 【yaml多行字符串】几种用法（`|`, `>`, `\n`），有啥区别
            - 【yaml多值映射】怎么搞（?和:分别用array标明key和val）
            - yaml spec中map类型的flow style是啥? 怎么用? 其实就是和平时使用的block style相对应的
          hto:
            - yaml有哪些问题？ # [YAML：可能并不是那么完美- 掘金](https://juejin.cn/post/6844903757277495310)
  record:
    - 【2025-06-24】移除【Xuanwo/gg（golang代码生成器）】
    - 【2025-06-29】把之前辛苦整理的【golang CHANGELOG】删掉了。需要的时候再自己写，这些东西没必要整理，太傻了。
  topics:


    # [垃圾回收的认识 | Go 程序员面试笔试宝典](https://golang.design/go-questions/memgc/principal/) 这个其实就是那个“Go GC 20 问”
    # [Go语言GC实现原理及源码分析 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/475)
    - topic: golang GC
      picDir: langs/GC
      why:
        - "***【三色标记】为什么golang会选择使用三色标记（并发增量），而不是分代或者复制了？那么为什么 golang 不使用分代或者复制 (整理) 方案？***" # 不分代是因为compiler本身实现了对新生代和老生代的分配。不复制是因为golang使用TCMalloc内存分配算法，不存在内存碎片问题。

        #  不分代的原因：
        #- *golang 编译器会通过逃逸分析把大部分新对象放到栈上，只有长期使用的老对象才分配到堆上*(协程结束后，栈直接回收，不需要 GC 参与)
        #- golang GC 的目标除了减少 STW，更重要的是让 mutator 和 collector 并行执行，而因为并发执行，所以 STW 的时间和对象的代际和 size 没有关系
        #  不复制的原因：
        #- *golang 使用 TCMalloc 内存分配算法，不存在内存碎片问题*(复制收集的原理是 xxx)
        #- 顺序内存分配器无法在并行场景下使用

        #  为什么 golang 要使用三色标记？
        #- 我们知道 golang 比较适合网络高并发的服务场景，那么如果使用 STW 时间较⻓的 GC 算法，对服务来说是致命的，故而要选用 STW 时间较少的算法，在标记清除的基础上发展来的三色标记出现了。
        #- 三色标记的思想其实是尽量把标记阶段、清除阶段与程序同时跑，它其实是一种增量式 GC 算法，所谓增量式其实就是把 GC 过程拆分出来的意思，跟我们要把最大的 STW 时间减少的思想吻合。

        - Golang的内存模型中为什么小对象多了会造成GC压力 # 通常小对象过多会导致GC三色法消耗过多的GPU。优化思路是，减少对象分配

        # [从源码剖析Go语言基于信号抢占式调度 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/485)
        - golang GC相关源码?
        # gc.go - 这个文件包含了垃圾回收的主要逻辑和控制流程。
        # mheap.go - 管理堆内存的源码，包括内存的分配和回收。
        # mgc.go - 包含了与并发垃圾回收相关的后台标记和清扫工作。
        # gcbss.go - 包含了BSS段的垃圾回收逻辑。
        # gcmark.go - 包含了标记阶段的实现细节。
        # gcsweep.go - 包含了清扫阶段的实现细节。
        # https://github.com/golang/go/tree/master/src/cmd/compile/internal/gc
        # [go/src/runtime/mgc.go at master · golang/go](https://github.com/golang/go/blob/master/src/runtime/mgc.go)

        - golang GC的演进历史? # [一文搞懂Go GC演进史，讲的太细致了！_Go_王中阳Go_InfoQ写作社区](https://xie.infoq.cn/article/f56b419e9de2e8ca3d44ee0ce)

        - 内存屏障是啥？为啥有了MESI还需要内存屏障？ “内存屏障其实就是编译器帮你生成的一段 hook 代码，这三种写屏障的本质区别就是 hook 的时机不同而已。内存屏障是 mutator 读取/创建/更新对象时，执行的一段代码我们用`内存屏障`保证内存操作的`有序性`，也就是，内存屏障前的指令优先于屏障后的指令执行”

        - 读屏障和写屏障的对比：为啥不用读屏障，而用写屏障呢？ # 归根到底是因为读操作远多于写操作，并且。读屏障需要在读操作中加入代码片段，对 mutator 的性能影响很大，所以不使用。写屏障则是 (通过记录新对象对老对象的引用)，对所有涉及修改的内容进行保护。写屏障类似一种开关，在 GC 的特定时机开启，开启后指针传递时，会把指针标记 也就是说，*本轮不回收，下次 GC 时再确定*。
        - 写屏障到底做了哪些操作？ # 写屏障就是在编译时hook住一部分写操作（并非全部写操作），把这部分操作放到buffer里，然后批量置灰
        #- hook 写操作
        #- hook 住了写操作之后，把赋值语句的前后两个值都记录下来，投入 buffer 队列
        #- buffer 攒满之后，批量刷到扫描队列（置灰）（这是 GO 1.10 左右引入的优化）

        - Dijkstra 插入屏障 和 Yuasa 删除屏障 # “为了确保强弱三色不变性的并发指针更新操作，需要通过赋值器屏障技术来保证指针的读写操作一致。因此我们所说的 Go 中的写屏障、混合写屏障，其实是指赋值器的写屏障，赋值器的写屏障作为一种同步机制，使赋值器在进行指针写操作时，能够“通知”回收器，进而不会破坏弱三色不变性。”

        # [golang 垃圾回收（五）混合写屏障](https://liqingqiya.github.io/golang/gc/%E5%9E%83%E5%9C%BE%E5%9B%9E%E6%94%B6/%E5%86%99%E5%B1%8F%E9%9A%9C/2020/07/24/gc5.html)
        - GC 为什么需要混合写屏障？ # Go 在 1.8 的时候为了简化 GC 的流程，同时减少标记终止阶段的重扫成本，将 Dijkstra 插入屏障和 Yuasa 删除屏障进行混合，形成混合写屏障。该屏障提出时的基本思想是：对正在被覆盖的对象进行着色，且如果当前栈未扫描完成，则同样对指针进行着色。【golang就是结合两种写屏障，实现了混合写屏障。】

        - 插入写屏障和删除写屏障的时机和区别？golang 中怎么实现的？
        - 混合写屏障
        #- 混合写屏障继承了插入写屏障的优点，起始无需 STW 打快照，直接井发扫描垃圾即可；
        #- 混合写屏障继承了删除写屏障的优点，赋值器是黑色赋值器，GC 期间，任何在栈上创建的新对象，均为黑色。扫描过一次就不需要扫描了，这样就消除了插入写屏障时期最后 STW 的重新扫描栈；混合写屏障扫描精度继承了删除写屏障，比插入写屏障更低，随着带来的 是 GC 过程全程无

        - 强三色和弱三色
        #- 强三色：黑色对象不能指向白色对象，只能灰色或者黑色对象
        #- 弱三色：黑色对象指向的白色对象，必须包含一条从灰色对象经由多个白色对象的可达路径

        - 强三色不变式：灰色或者黑色对象的引用改为白色对象的时候，Golang是该如何操作的？ # 这时候，写屏障机制被触发，向GC发送信号，GC重新扫描对象并标位灰色。因此，gc一旦开始，无论是创建对象还是对象的引用改变，都会先变为灰色。

        - "***golang GC的大概执行过程（四个阶段）？STW 发生在什么时候（GC中STW时机）？***"
        - golang如何实现的“并发三色标记扫描”？
        - golang GC现在还存在哪些问题
        - golang GC有哪些优化？为何需要辅助标记和辅助清扫？
        - golang为什么选择使用三色标记，而不是分代或者复制？
        - 什么时候启动GC？
        - 通过哪些指标来判断要启动GC？
        - GC应该如何与scheduler进行交互？
        - 如何暂停一个mutator线程足够长时间，以扫描器stack？
        - 如何表示white、grey和black三种颜色来实现高效地查找、扫描grey对象？
        - 如何知道roots对象在哪里？
        - 如何知道一个指向对象的指针的位置？
        - 如何最小化内存碎片？
        - 如何解决cache性能问题？
        - heap应该设置为多大？
        - 怎么调优golang GC？

        - GC 的四个阶段？
        - 为什么需要辅助标记和辅助清扫？
        - GC 的四个阶段，STW 发生在什么时候？GC 中 stw 时机，各个阶段是怎么解决的？
        #以下是Go GC中的STW时机和如何解决的：
        #- 开始STW：在开始标记阶段之前，需要进行一次短暂的STW，以确保所有的P（处理器）都停止在安全点，即一个不会修改堆的位置。这个STW时机是必要的，因为它能确保在标记阶段开始时，所有的goroutine都不会创建新的对象或修改现有对象的指针。这个STW的时间通常非常短。
        #- 结束STW：在标记阶段结束后，需要进行一次短暂的STW，以确保所有的goroutine都停止在安全点，然后进行最后一次的标记，并开始清除阶段。这个STW时机也是必要的，因为它能确保在清除阶段开始时，所有的对象都已经被正确地标记。这个STW的时间也通常非常短。
        #在标记阶段，Go的GC采用了并发标记的策略，即在goroutine运行的同时进行标记。这是通过写屏障（Write Barrier）实现的，写屏障在每次写入指针时都会标记该指针。这样，即使在标记阶段有新的对象被分配或旧的对象被更新，GC也能正确地标记这些对象。
        #在清除阶段，Go的GC采用了并发清除的策略，即在goroutine运行的同时进行清除。这是通过延迟清除（Lazy Sweeping）实现的，即只在需要分配新的对象时才清除那个对象所在的内存块。

        - GC 触发时机？主动触发 runtime.GC，周期被动触发，Pacing 算法（其核心思想是控制内存增长的比例。如 Go 的 GC 是一种比例 GC, 下一次 GC 结束时的堆大小和上一次 GC 存活堆大小成比例.）

        - 描述一下 GC 调步算法的实现？
        - GC 清扫阶段 对象回收和内存单元的联系和差异？
        - 根对象到底是什么？
        - 有了 GC，为什么还会发生内存泄露？
        - 并发标记清除法的难点是什么？ # 在没有用户态代码并发修改 三色抽象的情况下，回收可以正常结束。但是并发回收的根本问题在于，用户态代码在回收过程中会并发地更新对象图，从而造成赋值器和回收器可能对对象图的结构产生不同的认知。这时以一个固定的三色波面作为回收过程前进的边界则不再合理。【总结：并发标记清除中面临的一个根本问题就是如何保证标记与清除过程的正确性。】

        - 有了 GC，为什么还会发生内存泄露？（1、预期能被快速释放的内存，因为被根对象引用，而没有得到迅速释放。2、goroutine 泄露）
        - "***通过保证三色不变式来保证回收的正确性，通过写屏障来实现业务赋值器和 gc 回收器正确的并发的逻辑***"

        - “STW 是全局的赋值器挂起，我们一直说 golang 消除了 STW 说的是没有了全局性的挂起，但是局部的赋值器挂起是一直有的，包括现在也是有的。”


    # strings.TrimLeft()/strings.TrimRight() 和 strings.TrimPrefix()/strings.TrimSuffix()的区别？ # 类似需求使用`prefix/suffix`；`left/right`会依次查找每个字符，如果该字符在 cutset 内，就会被移除，直到遇到第一个不在 cutset 中的字符；比如`strings.TrimRight("123aabc", "abc")`会返回`123`而不是`123a`
    # Compare(), Contains()(Any()/Func()/Rune()), Count(), Cut(), CutPrefix(), CutSuffix(), Fields()(Func()), HasPrefix()/HasSuffix(), Index()/LastIndex()(Any()/Byte()/Func()/Rune()), Join(), Map(), Repeat(), Replace()/ReplaceAll(), Split()/SplitAfter()/SplitN()/SplitAfterN(), ToLower()/ToUpper()/..., Trim()/...
    # [fmt.Printf formatting tutorial and cheat sheet · YourBasic Go](https://yourbasic.org/golang/fmt-printf-reference-cheat-sheet/)
    - topic: string
      hti:
        - 【字符串实现】为啥 golang stringStruct 只有 str 和 len，没有 cap 呢?  # 我看了一下源码，c++ 和 rust 的字符串都是 pointer/len/cap 的结构（我又查了一下 rust 提供了两个字符串结构，&str 是不可变类型，String 则是通过添加 cap 实现了可变类型），但是 golang 只有 pointer 和 len，这是基于什么考虑？那使用指针，和直接把字符串存储到 struct 里，有什么区别呢？哪个效率更高？ # 字符串本身是不可变的，以确保字符串在被多个协程同时访问时不会出现竞争条件 # string 没有 cap，所以 string 是个只读类型，这点可以对比 slice（slice 就有 cap，所以可以修改）
      htu:
        - 【strings相关操作】
        - 【字符串格式化】
        - 【字符串拼接】核心在于优化性能（需要考虑是否会频繁出现内存分配和数据拷贝操作），所以怎么分析 strings.Builder{}、fmt.Sprintf()、直接拼接等几种方法的内存分配和数据拷贝次数?
      hto:
        - 怎么避免 golang substring操作导致内存泄漏?


    # [【Go】slice的一些使用技巧 - 知乎](https://zhuanlan.zhihu.com/p/55662881)
    # [Go 常见错误集锦 | append 操作 slice 时的副作用从一个切片切分成子切片时，在这两个切片之间有可能会产 - 掘金](https://juejin.cn/post/7064938788824285215)
    - topic: slice
      hti:
        - (pointer+cap+len). Why cap? (相当于pool，允许切片在一定范围内动态增长，避免频繁的内存分配操作) + How does slice create and expand?
        - 【扩容机制】
        - 【伪缩容机制】具体过程？
        - 【slice是否原生支持并发】Why? 如果想让slice并发安全，怎么实现?

      # slice和map都是pseudo shrink，且回收都要靠GC来完成（事实上只要是带GC的语言都是pseudo shrink，都是靠GC完成回收）三种：c/cpp这种无GC的真缩容、带GC的都是伪缩容、rust

      # 简单来说就是，切片在扩容时会进行内存对齐，这个和内存分配策略相关。进行内存对齐之后，向 slice 追加元素时，判断原 slice 容量(oldCap)是否大于1024，小于则翻倍扩容（指的是新slice的容量变为oldCap的两倍），大于则1.25倍。 # (growslice, 特殊情况（如果申请扩容的容量cap 大于 oldCap的两倍，就直接扩容为cap）、oldCap < 256(threshold) 翻倍扩容, >256 通过for获取到newcap的值并赋值, 更平滑)

      # [你说 Go 的切片原生支持并发吗？](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247497587&idx=1&sn=85095b42b1f5f0ba753bb0174fbcfbe3&poc_token=HKxyNmWjeO_G0QPaMZ1yp1hdHPYFl4DkfGnMhlRK) 并发写操作肯定是不安全的。至于并发读，如果定义了len，并发读是安全的，因为一旦定义len，就不能在不重新分配底层数组的情况下改变。解决方案无非是把并发变成串行：用Mutex；用chan；用sync.Map代替slice。

      htu:
        - 【slice初始化】为什么在初始化 slice 的时候尽量补全 cap？如果不设置 cap，make slice 的时候，创建的 cap 为多大？
        - 【合并slice】比较 append(), copy(), slices.Concat()
        - 【执行slice深拷贝】
        - 【slices内置操作】
        - append 无法修改传入参数 slice，怎么处理？
        - 【golang slices pkg】Clip(), Clone(), Grow(), Insert(), Replace(), Reverse(), Func(BinarySearch(), Compact(), Compare(), Contains(), Delete(), Equal(), IsSorted(), Max(), Min(), Index(), Sort())

      # [Go 官方 Slice 教程图解版](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651445035&idx=3&sn=7800fa08fe3f83015635dc3f2febb6fd) append 适用于零长度的初始化 slice，copy 适用于定长
      # [append修改slice](
      # [Remove elements from slice of struct](
      # [【Go】slice的一些使用技巧 | thinkeridea博客](https://blog.thinkeridea.com/201901/go/slice_de_yi_xie_shi_yong_ji_qiao.html)
      # [Go Slice Tricks Cheat Sheet](https://ueokande.github.io/go-slice-tricks/)


      hto:
        - 【内存泄漏】具体聊聊slice因为切片容量和指针类型，导致内存泄漏的具体场景?

      # [Go 中切片使用不当会造成内存泄漏的那些场景 - 掘金](https://juejin.cn/post/7065683956871462943)
      # [Go 切片导致内存泄露，被坑两次了！](https://mp.weixin.qq.com/s?__biz=MzUxMDI4MDc1NA==&mid=2247492355&idx=1&sn=0e468b75394ba9778437b5c72e43c3ad)
      #- 因切片容量而导致内存泄漏：在已有的切片或数组上进行切分操作而保留了原有切片的容量大小导致内存泄露。如果我们在一个大的切片上只切分出一个小的切片，那么大量内存将会保持分配状态但没有得到应用。
      #- 因指针类型导致内存泄露：当我们在切分一个元素类型为指针类型的切片或切片的类型是含有指针字段的结构体时，GC不会自动回收这些元素。在我们列举的例子中，我们通过将剩余元素手动置为nil已达到自动回收的目的。



    - topic: map
      hti:
      htu:
        - golang 修改map中的元素（为啥map不能直接修改map）? 另外，怎么修改map of struct中的的值 (原地修改map)?
        - How to convert []map to []struct?
        - map[string]interface{} to struct
        - 怎么判断map是否包含某个key? (comma ok idiom (whether err/kv/chan exist, 类型推导))
        - 手写实现 golang 中 map 的有序查找，且支持 add、支持 delete、支持迭代？构造一个辅助 slice
      # [HashMap](
      hto:
        - slice 和 map 作为函数参数时，有什么区别？
        - golang map 并发读安全吗？换句话说，sync.Map到底是用来保证golang map并发读还是并发写的？

      # 函数内对 map 的操作会影响 map 自身，对 slice 操作则不会；因为 map 的底层结构是指针`hmap`，slice 的底层结构是 struct。golang中的函数传参都是值传递，在函数内部，参数会被 copy 到本地。`hmap`指针 copy 完之后，仍然指向同一个 map，因此函数内部对 map 的操作会影响实参。而 slice 被 copy 后，会成为一个新的 slice，对它进行的操作不会影响到实参。

      # 只是golang禁止并发读写map操作，而不是并发读本身不安全。


    - topic: struct
      hti:
        - 【实现原理】How tag works? # reflect. CPU clock cycle, minimize the size of the struct(in memory), Data Types in golang(bool(1), int/map(8), string(16), slice(24))
      htu:
        - 【空struct】为什么空结构体的size为 0？ 空struct 有哪些使用场景？
        - struct 能否进行比较? 怎么比较?
        - 【fieldalignment】原理? 为啥需要内存对齐?

      # 因为 golang 编译器在内存分配时的优化项，当发现 size 为 0 时，会直接返回变量`zerobase`当引用，该变量是所有 0 字节的基准地址，不占据任何宽度。最主要原因使用空 struct 可以节省内存，一般作为占位符使用，表示这里不需要一个值（空结构体的宽度为 0，而其他类型即使只是声明，也占据了一定的宽度（就是`unsafe.Sizeof()`））
      # 1、用空结构体实现Set类型。2、实现空chan（也就是`make(chan struct{})`，通过`空chan`控制 chan）
      # 用go-cmp（而不是reflect.DeepEqual，更不是直接===）进行比较（因为DeepEqual默认会比较未导出字段，go-cmp可以自定义设置）。 filed类型是否comparable
      # 为了 CPU 更高效访问内存中数据，CPU 访问内存时，不是逐个字节访问，而是以`字长`为单位访问，这么设计的目的是为了减少 CPU 访问内存的次数，加大 CPU 访问内存的吞吐量。如果不进行内存对齐，就很可能增加 CPU 访问内存的次数
      hto: # 1、struct 里的私有属性，无法赋值（无法被 encode，json.Marshal() 时私有属性就丢了，要注意字段的大小写）。2、对 struct 尽量使用指针（可以更快传递数据，也避免了无法修改原数据的情况）


    - topic: interface
      hti:
        - (eface// iface) # [深入研究 Go interface 底层实现](https://halfrost.com/go_interface/) eface就是empty interface, iface则相反. type内置了各种数据结构, data指针。这两个结合之后，其实很类似string的数据结构。iface的数据结构也很类似，区别在于itab，itab是把eface的type又包了一层。 type 接口自身的元信息。hash _type里也有一个同样的hash，此处多放一个是为了方便运行接口断言。fun 函数指针，指向具体类型所实现的方法。
      htu:
        - interface的动态转发和反射
        - interface的类型转换
        - 怎么判断 interface 变量存的是哪种类型？ # em 是 interface 类型的变量，T 代表要断言的类型，value 是 interface 变量存储的值，ok 是 bool 类型表示是否为该断言的类型 T。
      hto:

    - topic: defer
      hti:
        - defer 变量快照
        - defer 链表是如何被遍历并执行的？
      htu:
        - 延迟调用、LIFO、异常场景（defer 可以无视 panic 继续执行，所以可以用来 recover）
        - 1、defer 和 return 的返回顺序？defer 在 return 之前执行，还是在 return 之后执行？怎么理解“defer 是在函数调用返回时执行的”？
        - 2、如果有多个defer，返回顺序?

      # Delayed call, LIFO, recover panic, close(file, conn, mutex, chan, goroutine...)
      # [说好 defer 在 return 之后执行，为什么结果却不是？](https://mp.weixin.qq.com/s?__biz=MzUxMDI4MDc1NA==&mid=2247489727&idx=2&sn=9a1ce9f8835b68fe38352fdc2b16e9cb) [深入剖析 defer 原理篇 —— 函数调用的原理？](https://mp.weixin.qq.com/s?__biz=Mzg3NTU3OTgxOA==&mid=2247486774&idx=1&sn=3b59ac2efc97b7bbebbde366d0ee4ea0&source=41#wechat_redirect) 结论：因为执行 return 不是“原子操作”，实际上分为“返回值赋值”和“return”两个部分，我们回到 defer 最本质的语义‘defer 是在函数调用返回时执行的’，所以实际上 defer 操作是在这两步操作之间进行的。

      hto:
        - 怎么避免`defer Close()`存在的 EIO 问题？ # [你有考虑过 defer Close() 的风险吗](https://mp.weixin.qq.com/s?__biz=MzUxMDI4MDc1NA==&mid=2247491855&idx=2&sn=01d5e119904f894357581a94596a1c89) 意义不大，并且偏底层，这些东西应该是golang本身或者各种库本身就应该处理好的，了解一下就可以了。



    # FIXME 自己把下面这些弄一下
    # https://pkg.go.dev/encoding/json
    - topic: golang encoding/json pkg
      url: https://colobu.com/2017/06/21/json-tricks-in-Go/
      qs:
        - struct tag是啥? # tag 就是 struct 中字段的注解。用来定义字段的属性，可以通过反射获取某个字段定义的属性，进行相应处理。比如*标注类型为 json 的 tag，就可以进行结构体字段和 json 字段的转换*
        - 怎么使用 struct tag，在序列化时忽略指定字段（也就是说即使不为空，也要忽略）? # -
        - 怎么使用 struct tag，在序列化时忽略空值字段? # omitempty
        - "***使用 omitempty需要注意哪些坑?***" # [Go 中 “omitempty” 的陷阱](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651452388&idx=1&sn=f9799e636c3be0e56741aad7baf482d2) 可以用指针类型解决这两个问题：正好需要空值，使用omitempty之后就不返回了（另一种情况实际上也可以归入这种情况：即使 struct 里嵌套的 struct 为空，输出的 json 也会返回）
        - 怎么 忽略嵌套struct空值字段
        - 怎么 不修改原结构体忽略空值字段
        - 怎么 忽略空值字段
        - 怎么 自定义解析时间字段

        - 临时忽略struct空字段
        - 临时添加额外的字段
        - 临时粘合两个struct
        - 一个json切分成两个struct
        - 临时改名struct的字段
        - 用字符串传递数字
        - 容忍字符串和数字互转、容忍空数组作为对象 # jsoniter 模糊模式
        - 使用 MarshalJSON支持time.Time # jsointer
        - 使用 RegisterTypeEncoder支持time.Time # jsointer对不是你定义的type自定义JSON编解码方式
        - 使用 MarshalText支持非字符串作为key的map
        - 使用 json.RawMessage
        - 使用 json.Number
        - 统一更改字段的命名风格
        - 使用私有的字段
        - 忽略掉一些字段
        - 忽略掉一些字段2

        - json反序列化时 # https://go.dev/play/p/-J16F7AmMXO
        - JSON pointer # https://go.dev/play/p/DjDLYB8ES5n
        - JSON handle

        # [Golang 中使用 JSON 时如何区分空字段和未设置字段？](https://mp.weixin.qq.com/s?__biz=MzI4NDM0MzIyMg==&mid=2247491057&idx=1&sn=ceaf1c699ef37e3819dde8ec1425c7f7)
        - "***Golang 中使用 JSON 时如何区分空字段和未设置字段？*** 具体来说，struct tag 如果设置为omitempty执行update操作时会把该field设置为空字符串，导致出错，怎么避免该问题？如果给该field赋值恰好等于默认空值的话，Marshal后为nil，又怎么解决该问题？" # [踩到 Go 的 json 解析坑了，如何才能严格解析 json？ - V2EX](https://www.v2ex.com/t/975214) # 给该field加指针类型。“通过这样做，我们在字段中添加了额外的状态。如果原始 JSON 中不存在该字段，则结构体字段将为空 (*nil*). 另一方面，如果该字段确实存在并且为空，则指针不为空，并且该字段包含空值.”

        - 为啥不推荐使用struct embeding? (导致json.Unmarshal出错)

        - 用jsoniter 模糊模式实现容忍字符串和数字互转，或者容忍空数组作为对象？
        - "***json 数字精度丢失问题***"
        - json 包的时间格式问题
        - Precision is lost when deserializing json in golang, How to resolve?
        - How to convert between struct, map and json in golang?
        #    - 使用 MarshalJSON支持time.Time
        #    - 使用 RegisterTypeEncoder支持time.Time
        #    - 使用 MarshalText支持非字符串作为key的map
        #    - 使用 json.RawMessage
        #    - 使用 json.Number
        #    - 使用 jsoniter 统一更改字段的命名风格
        #    - 使用私有的field

        - json.Unmarshal(non-pointer map[string]string) 问题 # https://juejin.cn/post/7063739889812144159





    - topic: mutex
      url: https://github.com/golang/go/blob/master/src/sync/mutex.go
      hti:
        - mutex 的 sema 和 state 有什么用?
        - mutex 的 正常模式和饥饿模式，这两种模式分别是啥? 切换流程(golang抢锁机制)? (在哪几种情况下mutex从normal切换到starvation模式? 什么情况下又切换回来?)
        - golang mutex CHANGELOG. “初版的 Mutex 实现有一个问题：请求锁的 goroutine 会排队等待获取互斥锁。虽然这貌似很公平，但是从性能上来看，却不是最优的。因为如果我们能够把锁交给正在占用 CPU 时间片的 goroutine 的话，那就不需要做上下文的切换，在高并发的情况下，可能会有更好的性能。”
      #  (sema), critical section 临界区.
      #  mutex 底层使用 atomic 包中的 CAS 操作来保证加锁时的原子性，CAS 底层就是通过 LOCK+CMPXCHGL 汇编指令实现的
      #  mutex 的 sema 有什么用？ *mutex 对 goroutine 的阻塞操作和唤醒操作就是通过 sema 实现的*，具体来说，runtime 维护了一个全局的变量 semtable，里面有全部的信号量，每个信号量都由一个变量地址指定

      # 切换的唯一条件就是goroutine获取锁的时间，超过1ms就切换到starvation。小于1ms（并且等待队列已经全部清空了）就切回normal

      #- `正常模式` 阻塞等待的 goroutine 保存在 FIFO 队列中，唤醒的 goroutine 不直接拥有锁，需要与新来的 goroutine 竞争获取锁。因为新来的 goroutine 很多已经占有了 CPU，所以唤醒的 goroutine 在竞争中很容易输；但如果一个 goroutine 获取锁失败超过 1ms，则会将 Mutex 切换为饥饿模式。
      #- `饥饿模式` 直接将`等待队列中队头的 goroutine`直接解锁，新来的 goroutine 也不会尝试获得锁，而是直接插入到`等待队列的队尾`。


      htu:
        - TryLock(), IsLocked(), IsWoken(), IsStarving(). （对state的处理）
        - 【spinlock】spinlock 和 TryLock 的实现很类似，都是加个了 CAS 操作。TryLock是单次CAS操作，而spinlock则是在for循环了 CAS+runtime.Gosched()让出CPU，直到CAS通过获取到锁
        - 【ReentrantMutex】为啥 golang 的 mutex （为了避免死锁，设计为）不可重入呢？怎么实现？
        - 【超时直接解锁】用 mutex 实现一个带 timeout 的锁？
        - 【常见错误使用】成对使用、nocopy、不可重入、死锁


      # https://go.dev/play/p/lw5jSOdpRqh 其实就是提取 state 字段的中数据，state是个int32类型的数据，从0~3分别代表 mutexLocked, mutexWoken, mutexStarving, mutexWaiterShift
      #- `mutexLocked`（第 0 位）：表示互斥锁是否被锁定。如果这一位被设置，那么互斥锁就是锁定的。
      #- `mutexWoken`（第 1 位）：表示是否有等待的 goroutine 被唤醒。如果这一位被设置，那么表示有一个等待的 goroutine 被唤醒并准备获取锁。
      #- `mutexStarving`（第 2 位）：表示互斥锁是否处于饥饿模式。在饥饿模式下，锁的所有权会直接从解锁的 goroutine 传递给等待队列中的下一个 goroutine，新来的 goroutine 不会尝试去获取锁，即使锁看起来是无锁的状态。
      #- `mutexWaiterShift`（第 3 位开始）：表示等待获取锁的 goroutine 的数量。

      # 这样的设计决策可以避免潜在的死锁问题，因为重入锁的正确使用需要非常小心，以确保在释放锁之前所有锁的请求都已经完成。如果出现错误的重入，可能导致死锁或其他不可预测的行为。如果需要支持重入锁，可以使用 `sync.RWMutex`，它支持读写锁，允许多个 goroutine 同时读取共享资源，但只允许一个 goroutine 写入资源。`sync.RWMutex` 是可重入的，但仍然需要小心地确保正确地获取和释放锁。 # [Go 语言如何实现可重入锁？](https://mp.weixin.qq.com/s?__biz=MzkyNzI1NzM5NQ==&mid=2247484797&idx=1&sn=ae62189e0118057dc1569b8123bc194e&source=41#wechat_redirect) 关键是获取goid。mutex 不是可重入锁，因为 mutex 的实现本身就是没有记录哪个协程持有锁，所以当然是不可重入的。所以想实现可重入锁，核心就是记录持有锁的协程。方法无非是两种，获取 goid，或者直接使用 uuid 进行标识。

      # [Go1.18 新特性：被折腾 N 次的 TryLock](https://mp.weixin.qq.com/s?__biz=MzI4NDM0MzIyMg==&mid=2247490221&idx=1&sn=c9bffd74786a4db695f03f6dce680b53)

    - topic: RWMutex
      url: https://github.com/golang/go/blob/master/src/sync/rwmutex.go
      hti:
        - 【读写锁的设计方案】为啥golang的RWMutex在解决 readers-writers problems 时，选择write-preferring而不是read-preferring呢?
        - 为啥说 RWMutex = mutex + 条件变量(conditional variables) + sema?
        - 读锁获取/释放 锁流程? 写锁获取/释放 锁流程? （换句话说，Lock()/Unlock()，RLock()/RUnlock() 的具体实现）
      #  readers-writers 问题一般有三类，基于对读和写操作的优先级，读写锁的设计和实现也分成三类。
      #  Read-preferring：读优先的设计可以提供很高的并发性，但是，在竞争激烈的情况下可能会导致写饥饿。这是因为，如果有大量的读，这种设计会导致只有所有的读都释放了锁之后，写才可能获取到锁。
      #  Write-preferring：写优先的设计意味着，如果已经有一个 writer 在等待请求锁的话，它会阻止新来的请求锁的 reader 获取到锁，所以优先保障 writer。当然，如果有一些 reader 已经请求了锁的话，新请求的 writer 也会等待已经存在的 reader 都释放锁之后才能获取。所以，写优先级设计中的优先权是针对新来的请求而言的。这种设计主要避免了 writer 的饥饿问题。
      #  不指定优先级：这种设计比较简单，不区分 reader 和 writer 优先级，某些场景下这种不指定优先级的设计反而更有效，因为第一类优先级会导致写饥饿，第二类优先级可能会导致读饥饿，这种不指定优先级的访问不再区分读写，大家都是同一个优先级，解决了饥饿的问题。
      #  Go 标准库中的 RWMutex 设计是 Write-preferring 方案。一个正在阻塞的 Lock 调用会排除新的 reader 请求到锁。

      # 具体来说，读写两个sema（writerSem、readerSem）、readerCount（记录当前 reader 的数量）、readerWait（记录 writer 请求锁时需要等待 read 完成的 reader 的数量）、rwmutexMaxReaders（定义了最大的 reader 数量）
      #  struct (w, writerSem, readerSem, readerCount, readerWait, rwmutexMaxReaders)
      #- `w` 互斥锁解决多个 writer 的竞争，为 writer 的竞争锁而设计
      #- `writerSem`writer 信号量
      #- `readerSem`reader 信号量
      #- `readerCount`记录当前 reader 的数量 (以及是否有 writer 竞争锁)
      #- `readerWait`，记录 writer 请求锁时需要等待 read 完成的 reader 的数量
      #- `rwmutexMaxReaders`定义了最大的 reader 数量


      htu:
        - 【互斥锁和读写锁的性能比较】怎么选择使用mutex还是RWMutex? 各自什么场景更合适?
        - 【常见错误使用】不可复制、不可重入、释放未加锁的 RWMutex
      # [读写锁和互斥锁的性能比较 | Go 语言高性能编程 | 极客兔兔](https://geektutu.com/post/hpg-mutex.html) 读写比
      #  读写比为 9:1 时，读写锁的性能约为互斥锁的 8 倍
      #  读写比为 1:9 时，读写锁性能相当
      #  读写比为 5:5 时，读写锁的性能约为互斥锁的 2 倍

    - topic: wg
      url: https://github.com/golang/go/blob/master/src/sync/waitgroup.go
      hti:
        - Add、Done 和 Wait 三个方法的具体实现?
        - wg 内部怎么实现无锁操作?
        - 为啥wg是值传递? noCopy机制
      htu:
        - 怎么让wg的Wait方法支持WaitTimeout?
        - 【ErrorGroup】是不是相较于 weight group ，我们用 error group 是一个更好的选择？换句话说就是大多数用 weight group 的场景下，都应该用 error group 去进行代替，对吧？但是正常来说，我们的业务代码里面几乎不会出现说你举的这个 weight group 的这种例子，一定是会抛错的。另外我想知道，就是 error group 有没有办法说是即使出错，即使 goroutine 出错，整个任务组依然正常运行，还是说它默认就是一旦 goroutine 出错，整个 IO group 就直接直接停止运行，并且抛错。
      #  (state1(counter, waiter, sema), noCopy), align64. 64bit(8bytes) 的值分成两段，高 32bit 是计数值，低 32bit 是 waiter 的计数
      #  通过结构体字段`state1`维护了两个计数器和一个信号量，计数器分别是通过`Add()`添加的子 goroutine 的计数值 counter，通过`Wait()`陷入阻塞的 waiter 数，信号量用于阻塞与唤醒 Waiter。
      #  当执行`Add(positive n)`时，counter +=n，表明新增 n 个子 goroutine 执行任务。
      #  每个子 goroutine 完成任务之后，需要调用`Done()`函数将 counter 值减 1，当最后一个子 goroutine 完成时，counter 值会是 0，此时就需要唤醒阻塞在`Wait()`调用中的 Waiter


    - topic: sync.Once
      url: https://github.com/golang/go/blob/master/src/sync/once.go
      hti:
        - sync.Once 是如何保证只执行一次的? 怎么实现“延迟初始化”的?
        - 为什么 sync.Once 的 Do 方法中没有使用 CAS 原子判断？ atomic(CAS, preliminary check) + Double-checked locking, singleton
      # sync.Once 使用内部的 Mutex 来保证并发安全。它首先尝试通过atomic检查 done 标志是否为 0，如果是，则进入doSlow()，在doSlow()中，它会加锁并再次检查 done 标志。如果仍然为 0，则执行函数并设置 done 标志为 1。这个过程中使用 defer 来确保即使函数执行过程中发生 panic，done 标志也能被正确设置。
      # 换种说法：一个正确的 sync.Once() 要实现一个互斥锁，这样初始化的时候，如果有并发的 goroutine，就会进入 doSlow() 方法。互斥锁的机制保证只有一个 goroutine 进行初始化，同时利用`双检查的机制 (double-checking)`，再次判断 o.done 是否为 0，如果为 0，则是第一次执行，执行完毕后，就把 o.done 设置为 1，然后释放锁。即使此时有多个 goroutine 同时进入了 doSlow 方法，因为双检查的机制，后续的 goroutine 会看到 o.done 的值为 1，也不会再次执行 f。这样既保证了并发的 goroutine 会等待 f 完成，而且还不会多次执行 f

      # 使用 CAS 原子判断虽然看似能提高性能，但它无法保证在 o.done 变为 1 时，传入的函数 f 已经完全执行。sync.Once 不仅要保证只执行一次，还要保证其他 goroutine 在 Once.Do 返回时，f 函数的执行已经完成。

      htu:
        - 为啥实际开发中，很少使用 sync.Cond?
        - cond 的 broadcast 和 signal 有啥区别？
        - 【常见错误使用】sync.Cond 错误用法?
      # sync.Once 适用于需要延迟加载或单次初始化的场景，例如加载配置、初始化日志系统、创建单例对象等。它可以确保即使在高并发环境下，这些操作也只被执行一次（经典使用场景：*某个值不存在则赋值 (或者某个对象不存在则创建对象)* 因为是并发场景，所以所有 goroutine 都会判断得到对象不存在，然后去创建;如果不控制，就会导致重复创建 (对象不断地被替换和丢弃);这种问题可以用sync.Once实现），从而避免资源浪费和潜在的竞态条件。 # 从开发实践上，我们真正使用 Cond 的场景比较少。因为一旦遇到需要使用 Cond 的场景，我们更多地会使用 chan 的方式去实现 wait/notify 机制，因为那才是更地道的 golang 的写法。对于简单的 wait/notify 场景，比如等待一组 goroutine 完成之后继续执行余下代码，我们会使用 waitgroup 实现，使用简单还不容易出错。
      # 1、*调用 cond.Wait 方法之前一定要加锁* 2、*waiter goroutine 被唤醒不等于等待条件被满足*(只调用了一次 Wait，没有检查等待条件是否满足，结果条件没满足，程序就继续执行了)。出现这个问题的原因在于，误以为 Cond 的使用，就像 WaitGroup 那样调用一下 Wait 方法等待那么简单。


    - topic: sync.Pool
      url: https://github.com/golang/go/blob/master/src/sync/pool.go
      hti:
        - sync.Pool 是怎么实现 thread-safe 的? # (temp data, GC, buffer pool) Pool 本身就是线程安全的 (可以并发地调用它的方法存取对象)，并且是lock-free的，因为给每个 P 都分配 cache 数组，这样 cache 结构就不会有并发安全问题
      #(lock-free) (local+victim) *保存和复用临时对象，减少内存分配，降低 GC 压力*，但是这些对象会被 GC 定期清除，所以不要用来存数据库连接之类的长连接，*sync.Pool 最常用的场景就是 buffer 池*(缓冲池)(因为 byte slice 是经常被创建和销毁的一类对象，使用 buffer 池可以缓存已经创建的 byte slice)。Pool 使用两层回收策略 (local+victim) 避免性能波动。
      #
      #- *保存和复用临时对象，减少内存分配，降低 GC 压力*，但是这些对象会被 GC 定期清除，所以不要用来存数据库连接之类的长连接，*sync.Pool 最常用的场景就是 buffer 池*(缓冲池)(因为 byte slice 是经常被创建和销毁的一类对象，使用 buffer 池可以缓存已经创建的 byte slice)
      #- Pool 本身就是线程安全的 (可以并发地调用它的方法存取对象)，但是内部是无锁结构，原理是对每个 P 都分配 cache 数组，这样 cache 结构就不会有并发安全问题
      #- Pool 使用两层回收策略 (local+victim) 避免性能波动



    - topic: atomic
      why:
      what:
        - What's atomic? sync 包已经覆盖大部分并发场景了，为什么还要有 atomic? # atomic 提供了修改类型的原子操作`RMW(Read-Modify-Write)`和加载存储类型的原子操作 (Load 和 Store) 的 API。本质上来说atomic是基于“底层硬件”实现的，是lock-free的。而mutex则是基于atomic实现的，本质上还是加锁实现的。从使用角度出发，atomic用来锁变量，mutex之类的则用来锁业务逻辑。如果只是变量的话，可以用atomic代替mutex来减少锁冲突
      htu:
        - atomic 的 (Add/(Store+Load)/CAS) 分别怎么用?
        - 可以用 atomic 实现`Lock-Free 无锁队列`吗？ # [lock-free using atomic](
        - 什么是多线程下变量的读写问题？怎么解决？ # `atomic.Value{}`可以将任意数据类型的读写操作封装成原子性操作；保证在高并发场景下对 struct 类型的数据进行修改，不报错；
      hti:
        - atomic是怎么实现的？
        - atomic是怎么解决不同CPU架构的原子操作的指令不同的问题的? # atomic 提供了修改类型的原子操作`RMW(Read-Modify-Write)`和加载存储类型的原子操作 (Load 和 Store) 的 API。需要注意的是，所有原子操作方法的被操作数的参数必须是指针类型，通过指针变量可以获取被操作数载内存中的地址，从而施加特殊的 CPU 指令，确保同一时间只有一个 goroutine 能够进行操作。
        - 优化 lockfree 中 atomic.CAS() spin 忙轮询自旋引发的性能开销
      hto:
        - 【uber-go/atomic】 # uber-go/atomic比atomic更简洁，并且还提供了一些atomic本身没有提供的操作，比如sub, MarshalJSON, UnMarshalJSON 这些高频，但是需要自己实现的atomic操作。



    # [go/src/runtime/chan.go at master · golang/go](https://github.com/golang/go/blob/master/src/runtime/chan.go)
    - topic: "***golang chan***"
      table: # chan-state.svg
        - opt: close
          nil: panic
          closed: panic
          normal: ---

        - opt: read
          nil: 阻塞
          closed: 读到对应类型的零值
          normal: 正常读取。如果buffered chan为空，或者unbuffered chan没有等待发送者时会阻塞

        - opt: write
          nil: 阻塞
          closed: panic
          normal: 正常写入。如果unbuffered chan没有等待接受者，或者buffered chan buf满时会被阻塞
      why:
      what:
        - chan 的本质是什么？ *chan 实际上就是 linux 管道，也是对CSP思想的实现*
        - chan 分配在堆还是栈？哪些对象分配在堆上？哪些对象分配在栈上？

      ww:
        - chan的使用场景（数据交流、数据传递、信号通知、任务编排、锁（实现类似mutex机制））
        #- 数据交流：当作并发的 buffer 或者 queue，解决生产者 - 消费者问题。多个 goroutine 可以并发当作生产者（Producer）和消费者（Consumer）。
        #- 数据传递：一个 goroutine 将数据交给另一个 goroutine，相当于把数据的拥有权 (引用) 托付出去。
        #- 信号通知：一个 goroutine 可以将信号 (closing、closed、data ready 等) 传递给另一个或者另一组 goroutine。
        #- 任务编排：可以让一组 goroutine 按照一定的顺序并发或者串行的执行，这就是编排的功能。
        #- 锁：利用 Channel 也可以实现互斥锁的机制。
      htu:
        - How to use chan? # 事实上，chan的使用相当简单。比如说最简单的pipeline模式吧，先定义两个chan的变量，作为in和out（也可以理解为this和next），然后写两个go func，第一个pub，第二个sub。注意两个go func里都要close chan。最后再把outChan循环处理一下就可以了。几个需要注意的地方：go func里最好 `defer close()`，而不是直接在func最后写close。其次，最需要注意的就是最后的循环，也就是“遍历 chan”，无非是普通for循环和for...select两种。肯定推荐使用for...select，可以做更多类似超时、默认、ticker等自定义操作。
        - 怎么优雅的关闭一个 channel (才能避免出现panic或者内存泄漏)? # 1、不要在接受端关闭chan。2、如果有多个发送者，不要主动关闭 chan，除非所有发送者都完成了发送操作。3、使用 sync.Once 确保 channel 只被关闭一次，避免重复关闭导致 panic。

        - select 的概念？ # select的作用是同时监听多个case是否可以执行。select 会阻塞当前 goroutine 并等待多个 chan 中的一个达到可以收发的状态。
        - select 实现原理？OSELECT, OCASE # select 语句在编译期间会被转换为`OSELECT`节点，每个`OSELECT`节点都会持有一组`OCASE`节点，如果`OCASE`的执行条件为空，那就意味着这是一个`default`节点。
        # 分为四种情况

        # - `直接阻塞`(select 不存在任何 case)
        # - `单一管道`(select 只存在一个 case)
        # - `非阻塞操作`(select 存在两个 case，其中一个 case 是 default)
        # - `常见流程`(select 存在多个 case)

        - "***select基本使用：（3个feat，随机性、超时机制、必须命中（兜底））分别怎么？***" # [一文掌握 Go 语言 Select 的四大用法](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&amp;mid=2651438801&amp;idx=2&amp;sn=d98d32d05f8333b3aa9dd83d404f19da)

        # random select(avoid hungry), timeout, default

        # - `随机性`(select 会随机选择 case): 引入随机性为了避免饥饿问题发生; 如果我们按照顺序依次判断，那么后面的条件永远都会得不到执行;
        # - `必须命中`(select 必须命中某个 case，不命中，就阻塞)
        #   - *加 default 进行兜底* (实际上，加 default 只是规避死锁，实际上没走 chan)
        #   - *给 chan 赋值，让某个 case 可以命中*
        # - `设置expire`(避免死锁): 如果 case 始终没有收到数据，select 就会阻塞。但是我们不希望 select 一直阻塞，所以通过添加`case <-timeout`的方式，手动设置一个超时时间，一旦超时，直接走 timeout 的 case

        # *case 里通常来读取数据，但是实际上也可以写入数据*: select 的 case 只要求进行 chan 操作，不要求是读取还是写入。

        - 常见错误使用：select 死锁问题？
        - 如何检查chan是否已满？
        - 如何使用select语句在多个chan上进行连续读取？
        - select语句如何实现超时机制？



        - select, deadlock # [Go select 死锁的一个细节](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651450170&idx=1&sn=195d96d6067d5502c13e251872e7b0d5)

      hti:
        # [多图详解Go中的Channel源码 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/427)
        - "***How to implement chan? data structure?***" # [深入 Go 并发原语 — Channel 底层实现](https://halfrost.com/go_channel/) chan 就是个环形数组，包括 buffer、sendx 和 recvx 收发的位置 (ringbuffer 记录实现)、sendq、recv。当 chan 因为缓区不足而阻塞了队列，则使用DLL存储。chan 收发遵循先进先出 FIFO 的原则
        # 队列数据（qcount, dataqsiz, sendx, recvx） 类型数据（elemsize, elemtype） 等待队列（recvq, sendq） 状态（closed）

      hto: # golang chan pattern (pipeline, fan-in, fan-out, barrier, (future, workers-pool, pub/sub, or-chan))
        - 【pipeline模式】 # https://mp.weixin.qq.com/s?__biz=MzUxMDI4MDc1NA==&mid=2247493165&idx=2&sn=4b5086b389a423160ae372e7da84c0e0
        - 【fanin模式】、【fanout模式】
        - 【barrier模式】
        - Compare barrier and fan-in? # https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651449810&idx=3&sn=4a4cd9ac9573c86a52645dc6ebfb9dc0
      qs:
        - channel 3种state? # state则分为nil, active, closed三种
        - Compare chan types(buffered chan, unbuffered chan)? # capacity, is block. *是否有容量（也就是是否可以暂存值），以及是否阻塞* # 是否有容量（也就是是否可以暂存值）、是否阻塞（也就是是否同步，unbuffered chan 同步，buffered chan 异步）

        - golang 嵌套channel?

        - "***What happens when close/read/write a nil/closed/normal chan? 为啥不同状态的chan在执行读写操作时，会阻塞或者panic呢？怎么理解呢？***" # 向 nil chan 里写数据会阻塞，向 nil chan 取数据阻塞，向 close chan 写会 panic, 取会返回 0 值。

        - chan 的哪些操作会引发 panic？三种
        # - 主程序在读取一个没有生产者的 channel 时会被判断为死锁，如果是在新开的协程中是没有问题的，同理主程序在往没有消费者的协程中写入数据时也会发生死锁
        # - 当通道被两个协程操作时，如果一方因为阻塞导致另一放阻塞则会发生死锁，如下代码创建两个通道，开启两个协程 (主协程和子协程)，主协程从 c2 读取数据，子协程往 c1，c2 写入数据，因为 c1，c2 都是无缓冲通道，所以往 c1 写时会阻塞，从 c2 读取时也会会阻塞，从而发生死锁
        # - 并发执行多个不依赖的服务
        # - select 监听 pipeline，合并多个 pipeline 的值到一个


        - "***3种遍历chan的方法有啥区别?***" # [go - whats the difference between for loop with select and only select? - Stack Overflow](https://stackoverflow.com/questions/38333083/whats-the-difference-between-for-loop-with-select-and-only-select)
        #- for...range...
        #- for...(select...case...)
        #- for...range...(select...case...)

        - 为啥“不要在 for 循环中使用`select + time.After`的组合，而是应该用NewTimer做定时器”? # [分析 Go time.After 引起内存暴增 OOM 问题](https://mp.weixin.qq.com/s?__biz=MzI2MzEwNTY3OQ==&mid=2648982905&idx=1&sn=3ad8a9d47beadc2d6ae979e35ab7fc73) 在 for 循环里不要使用 select + time.After 的组合，否则golang 的 heapObjects 数减少，应该用NewTimer做定时器
        # for 循环里的 select 有两个 case，一个是 被其他 goroutine 不断输入任务的 chan，另一个是 time.After 定时器。当 queue 有任务时，那么 time.After 不会在该 select 里唤醒。而且，for 循环每次 select 的时候，都会实例化一个个新的定时器。该定时器在 3 分钟后，才会被激活，但是激活后已经跟 select 无引用关系，被 GC 给清理掉。换句话说，被遗弃的 time.After 定时任务还是在时间堆里面，定时任务未到期之前，是不会被 GC 清理的。

        - select 死锁问题 # [Go select 死锁的一个细节](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651450170&idx=1&sn=195d96d6067d5502c13e251872e7b0d5)
        - 在select语句中实现优先级chan：k8s在处理具体事件的时候，我们会希望 Node 的更新操作优先于 Pod 的更新（我们不希望等到PodUpdate队列被耗尽后，才开始从受污染的Node中清除pod），所以怎么在select语句中实现优先级呢？具体来说，如何确保当ch1和ch2同时达到就绪状态时，优先执行任务1，在没有任务1的时候再去执行任务2呢？ # [Go语言在select语句中实现优先级 | 李文周的博客](https://www.liwenzhou.com/posts/Go/priority-in-select/) for+LABEL  # [Selecting higher priority events over lower priority events : r/golang](https://www.reddit.com/r/golang/comments/wsymef/selecting_higher_priority_events_over_lower/)




    - topic: golang context
      url: https://github.com/golang/go/tree/master/src/context
      why:
      what:
        - context是啥? 使用场景? # [Context这三个应用场景，你知吗](https://mp.weixin.qq.com/s?__biz=MzU3NzEwNjI5OA==&mid=2247484744&idx=1&sn=417168befde46047a23aac23f9b92f30) # cancel goroutine, store kv,
      htu:
        - 【使用】WithValue(), WithCancel(), WithTimeout(), WithDeadline() 分别有啥用? 怎么用?

        #- `WithValue()`基于 parent Context 生成一个新的 Context，保存一个 kv 键值对，常常用来传递上下文
        #- `WithCancel()`方法返回 parent 的副本，只是副本中的`Done Channel`是新建的对象，他的类型是`cancelCtx`
        #- `WithTimeout()`其实和`WithDeadline()`一样，只不过一个参数是超时时间，一个参数是截止时间。(超时时间加上当前时间，其实就是截止时间)
        #- `WithDeadline()`会返回一个 parent 的副本，并且设置了一个不晚于参数 d 的截止时间，类型为 timerCtx
        - WithTimeout() 和 WithDeadline() 有啥区别？ # timeout用来控制goroutine的执行时间，ddl用来控制goroutine的最长执行时间。WithTimeout内部就是调用的WithDeadline方法（“withTimeout、WithDeadline不同在于WithTimeout将持续时间作为参数输入而不是时间对象，这两个方法使用哪个都是一样的，看业务场景和个人习惯了，因为本质withTimout内部也是调用的WithDeadline。”）

        - 有哪些常用的runtime库?
      hti:
        - 怎么实现context? context.Context 这个 interface 的构成？ # [Context源码，再度重相逢](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651450798&idx=3&sn=6f59d1c66137ee7b785644dfd7fb0679)
      hto:
        - 使用规范：使用 context 会导致哪些问题？怎么避免？ # 1、使用context.Background()而不是nil。2、WithValue最好使用自定义类型，而非string之类的





    - topic: golang pkg
      table:
        # [超全总结：Go 语言如何操作文件](https://mp.weixin.qq.com/s?__biz=MzUxMDI4MDc1NA==&mid=2247496305&idx=2&sn=8f3d13db24875c13fef12862b885f570)
        # [写了 30 多个 Go 常用文件操作的示例，收藏这一篇就够了_51CTO博客_go编译多个文件](https://blog.51cto.com/u_15289640/5840461)
        - name: fs
          usage: 文件操作

        # [slog](
        # [slog 终极指南](https://colobu.com/2024/03/10/slog-the-ultimate-guide/)
        - name: slog
        - name: time
          usage: 一些常用操作? 设置时区? # [Golang时间处理容易踩坑，小心损失百万](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453989&idx=1&sn=72088fb51d22d6a62fa427b27b455cad)
        - name: html.NewTokenizer() #
        - name: net/url # url使用url.URL定义，而不是string，方便操作 # [andygrunwald/go-trending](https://github.com/andygrunwald/go-trending/blob/master/trending.go#L58)

        # [go/src/reflect at master · golang/go](https://github.com/golang/go/tree/master/src/reflect)
        # [Go reflection 三定律与最佳实践](https://halfrost.com/go_reflection/)
        - name: reflect

        # [Go Playground - The Go Programming Language](https://go.dev/play/p/d8kzvdR5kjy)
        # [gena/generators/webstack.go at master · x1ah/gena](https://github.com/x1ah/gena/blob/master/generators/webstack.go#L19)
        - name: html/template
          usage: golang中渲染html的方法，用template而不是自己手动渲染html

        - name: http/httptrace # [Go 服务网络不通？net/http自带的这个工具帮你快速定位](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247497294&idx=1&sn=f598dba869600de5905998aeb083c535)


        # [在Go中如何正确重试请求 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/677)
        # [在 Go 中如何正确重试请求](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453489&idx=1&sn=431f819375b35ae578db87c16db89962)
        # fasthttp 的优势？ # fasthttp 使用goroutine-multi-conn，而非CPC(coroutine per conn)，减轻了runtime调度goroutine的压力，所以性能更好 fasthttp 使用 worker 复用协程 (`goroutine-multi-connection`)，减轻 runtime 调度协程的压力 (net/http 给每个请求都开一个协程 (`goroutine-per-connection`)) 2、fasthttp 会延迟解析 HTTP 请求的 body 数据 (节省了很多不需要直接操作 body 的场景的成本)
        - name: net/http
          usage: golang的 net/http 本身不支持retry功能，那怎么实现呢?


        - name: io.FS
          usage: go:embed # [Go 眼中的文件系统是什么？ io.FS](https://mp.weixin.qq.com/s?__biz=MzUxMDI4MDc1NA==&mid=2247497042&idx=2&sn=c8c3abfbe2475c92184128945544b599)


        # [分享一个 Go 的 IO 流并发的小技巧](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247497706&idx=1&sn=cc6e9fc5e951b88c3428c371dea19fac#rd)
        # [从头再读取 io.Reader: 覆水难收？](https://colobu.com/2023/09/24/reread-the-io-Reader/)
        # [go - What is the difference between io.TeeReader and io.Copy? - Stack Overflow](https://stackoverflow.com/questions/71523651/what-is-the-difference-between-io-teereader-and-io-copy)
        # [Example using io.TeeReader and io.Pipe](https://gist.github.com/miku/d8be387909fc11d1b446c5b4a85da686)
        - name: io
          usage: # golang的io流，无法用for循环把io.Copy操作的写入时间重叠起来，多次操作也只消耗1份时间，这样就会很慢。那怎么才能实现IO流并发呢？ teeReader做分流，pipe写转读，goroutine并发 #
      qs:
        - 【net/http】使用golang的HTTP请求时，怎么正确地给每个长连接请求设置timeout?, 应该加在http.Client的Timeout中，而不是http.Transport中 # [i/o timeout ， 希望你不要踩到这个net/http包的坑](https://mp.weixin.qq.com/s?__biz=MzkyOTU5MTc3NQ==&mid=2247499280&idx=2&sn=5437a3549daef00178845766519a4261) # [有趣的 Go HttpClient 超时机制](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453895&idx=1&sn=785280d070131ecbe35828a86c7cbe6c) 不要在`http.Transport`中设置超时，那是连接的超时，不是请求的超时。否则可能会出现莫名`io timeout`报错。请求的超时在创建`client`里设置。

        - "***【golang generics】类型参数 (类型形参param 和类型实参arg)、类型集 Type Set、类型union、约束类型推断***" #


    - topic: 《golang中使用FP》
      isX: true
      des: 能否帮我解答一个综合性问题，是否推荐在golang中使用FP呢？既然FP有很多好处，但是golang又不是真正的FP语言。那么是否推荐？
      why:
        - FP的核心价值是什么?
      what:
        - 怎么理解“函数式编程是声明式的子集，过程式编程是命令式的子集”
      where:
        - 【何时何地用 FP？】有哪些不适用于 FP 的场景？ # 总结：涉及大量可变状态和副作用（如 I/O 操作、数据库事务处理）、高度依赖对象生命周期和状态管理（如 GUI 应用、游戏开发）、具有复杂历史依赖和顺序依赖（如系统启动、长时间运行批处理任务）的代码不适合用函数式风格重写。
      htu:
        - 1、集合过滤（Filter）lo.Filter() 2、集合映射（Map）lo.Map() 3、数据聚合（Reduce） lo.Reduce() 4、错误处理链 lo.Try().OrElse().Error() 5、并发处理 lo.Async 6、回调封装 lo.Compose()  # 我觉得简单点说吧，你就是，你这样，你列举大概5~6个场景，六个六六段代码，就是我们用普通的去普通的正常的go浪代码去写是什么样子。然后对比一下，用你无论是你说的这个go funk, 还是说lo o就漏的那个，是是不是漏，好像就是漏漏，就是那个谁开发漏，开发漏的那个人，开发另外一个函数式编编程的那个go浪的那个库，无论是什么吧。反反正你用其中任何一个都可以对比的一个函数式编程的写法。然后并且说一下为什么用这种场景下用函数式编程更好。就是你大概写6个经典的应该用函数式编程的场景，够浪的代码的比较。

      qs:
        - 什么样的语言真正适合FP?
        - golang是否符合这些标准?

        - 在golang中如何合理使用FP特性?
        - golang 里应该无脑用函数式编程吗？
        - "***虽然函数式编程在绝大多数场景下，都是最优解，但是有哪些场景不适合使用函数式编程呢？***"
        - golang 函数式编程，有哪些常用操作？常用 pkg？
        - “不要将Go改造为“伪函数式语言” —— 缺乏类型推导、不可变数据、尾递归优化的语言注定无法高效支撑纯FP。” （不可变数据缺失 → 并发安全漏洞（golang的slice和map在扩容前共享底层存储 → 并发写入时数据竞争）、（无尾递归优化 → 栈溢出））
        - 【FP的性能问题】为啥golang的FP就有性能问题？haskell之类的FP语言就没有？



    - topic: "***golang GMP***"
      picDir: langs/golang/GMP
      why:
        - 【解耦调度】全局队列锁竞争如何解决？引入 P 管理本地队列，实现无锁调度和工作窃取。
        - 【抢占演进】协作式调度为何导致饿死？基于信号（SIGURG）强制中断长耗时 G。
      what:
        - 【核心组件】GMP 各司何职？G（轻量协程）、M（内核线程）、P（逻辑处理器，管理本地队列）。
        - 【映射关系】协程和线程为何需 M:N 映射？避免为每个 G 创建线程，通过 P 动态绑定提升复用率。
      ww:
        - 【I/O 密集型】高频网络请求如何优化？增加 P 数量（GOMAXPROCS = CPU核数 * 1.5~2.0）。
        - 【CPU 密集型】计算任务如何减少竞争？设 GOMAXPROCS = CPU核数，降低上下文切换。
        - 【资源限制】P 和 M 数量是否无限？P 受 GOMAXPROCS 限制；M 默认上限 10,000。
      htu:
        - 【协程数量控制】如何避免 Goroutine 爆炸？使用 worker pool（如 ants 库）限制并发数。
        - 【优雅关闭】如何安全终止协程？通过 context.Done() 信号或 channel 关闭通知。
        - 【定时器管理】如何防止 Timer 泄露？调用 defer ticker.Stop() 确保资源释放。
      hti:
        - 【调度流程】G 如何被调度？本地队列 → 全局队列 → Work Stealing（偷取其他 P 半数 G）。
        - 【阻塞处理】Hand Off 为何需释放 P？M 阻塞时释放 P 供其他 M 使用，避免线程闲置。
      hto:
        - 【参数调优】GOMAXPROCS 如何配置？I/O 密集型：增加 P 数量；CPU 密集型：匹配 CPU 核数。
        - 【诊断工具】如何定位 Goroutine 泄露？使用 pprof 分析火焰图，检查滞留 G 的堆栈。
      qs:
        # [详解Go语言调度循环源码实现 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/448)
        - "***GMP分别是啥? 协程和线程有哪几种映射关系？为啥协程(G)和线程(M)是M:N的映射关系? G和M直接绑定就可以了，为什么要有P (抢占式调度器相较于之前调度器的优势?) （能不能不要 P？为什么不在 M 上直接实现本地队列和工作窃取，而是用 P 实现？）（为啥说golang调度器是“协作式的抢占式调度，基于信号的抢占式调度器”）?***"
        # g 代表一个 goroutine，它包含：表示 goroutine 栈的一些字段，指示当前 goroutine 的状态，指示当前运行到的指令地址，也就是 PC 值。
        # m 表示内核线程，包含正在运行的 goroutine 等字段。
        # p 代表一个虚拟的 Processor，它维护一个处于 Runnable 状态的 g 队列，m 需要获得 p 才能运行 g。
        # 当然还有一个核心的结构体：sched，它总览全局。
        # Runtime 起始时会启动一些 G：垃圾回收的 G，执行调度的 G，运行用户代码的 G；并且会创建一个 M 用来开始 G 的运行。随着时间的推移，更多的 G 会被创建出来，更多的 M 也会被创建出来。
        # 当然，在 Go 的早期版本，并没有 p 这个结构体，m 必须从一个全局的队列里获取要运行的 g，因此需要获取一个全局的锁，当并发量大的时候，锁就成了瓶颈。后来在大神 Dmitry Vyokov 的实现里，加上了 p 结构体。每个 p 自己维护一个处于 Runnable 状态的 g 的队列，解决了原来的全局锁问题。

        - 可以把 GMP 类比成生产线，G 就是流水线上的小工，M 就是流水线
        - “线程由 CPU 调度是抢占式的，而协程由用户态调度是协作式的 (一个协程让出 CPU 之后，才执行下一个协程)”
        - GMP 调度策略（不如说是 goroutine 的“生老病死”更合适，几个方面都概括到了）

        - 为什么协程会被称为“用户态线程”？
        # - 协程就是用户态线程，本质就是用户态自己切换 CPU，在协程这一层次，线程=CPU。
        # - *线程分为内核态线程和用户态线程，用户态线程指的就是协程 (所以，狭义上，我们只把内核态线程称为线程)*，用户态线程需要绑定内核态线程，CPU 只能感知到内核态线程，感知不到用户态线程。
        # - 线程的调度是 OS 实现的，对开发者不可见，而协程是在用户态实现的，对开发者可见。这就是为什么协程会被称为“用户态线程”。我们自己可以控制协程的调度。
        # - 因为协程会在函数被暂停执行时，保存函数的运行状态，并且可以从保存的状态中恢复并继续运行。这不就是 OS 对线程的调度吗？线程也可以被暂停，操作系统保存线程运行状态，然后去调度其他线程。此后，该线程再次被分配 CPU 时还可以继续运行，就像没有被暂停过一样。

        - "*P 和 M 的数量是可以无限增加的吗？二者的数量分别由什么决定？最多多少？*"
        - P 和 M 是程序运行时就被创建好了吗？
        - 当 M0 将 G1 执行结束后，会怎么做？
        - processor 的大小是多少？
        - "*golang GMP CHANGELOG，怎么从单线程调度器，到多线程调度器，到任务窃取调度器，再到抢占式调度器*"
        - GMP 调度过程中存在哪些阻塞？ # 要不是os阻塞，要不就是应用程序阻塞，本质来说是一样的。什么文件读取、网络操作之类的耗时操作，或者time.Sleep()这种代码

        - "***GMP 运行机制/调度流程：golang异步抢占的整体流程（队列轮转、系统调用）?***"
        # 1、队列轮转：P会周期性的将G调度到M中执行，执行一段时间后，保存上下文，将G放到队列尾部，然后从队列中再取出一个G进行调度，P还会周期性的查看全局队列是否有G等待调度到M中执行。
        # 2、系统调用：当G0即将进入系统调用时，M0将释放P，进而某个空闲的M1获取P，继续执行P队列中剩下的G。M1的来源有可能是M的缓存池，也可能是新建的。
        # 3、当G0系统调用结束后，如果有空闲的P，则获取一个P，继续执行G0。如果没有，则将G0放入全局队列，等待被其他的P调度。然后M0将进入缓存池睡眠。

        #            给正在运行的两个线程命名为 M1 和 M2
        #            1. M1 发送中断信号 (`signalM(mp, sigPreempt)`)
        #            2. M2 收到信号，操作系统中断其执行代码，并切换到信号处理函数 (`sighandler(signum, info, ctxt, gp)`)
        #            3. M2 修改执行的上下文，并恢复到修改后的位置 (`asyncPreempt`)
        #            4. 重新进入调度循环，而调度其他协程 (`preemptPark`和`gopreempt_m`)

        - "***work stealing, handoff 分别是啥? handoff为啥要释放掉线程绑定的processor，转移给其他空闲thread执行呢? 按理说，thread和processor不是一一绑定的嘛?***"
        #            都是为了“复用线程”，避免频繁创建和销毁thread
        #
        #            还是用公司项目做类比，如果你们项目组本身没有什么任务（M上没有G），那么就从其他项目组抢任务。这个就是work stealing嘛。如果你们项目组因为某个任务阻塞掉了（需要注意的是，这里实际上对应的thread的blocked或者waiting状态，而不是直接挂掉了），那就把这个项目所绑定的项目经理（也就是Processor，用来调度任务）释放掉，把这个项目经理让给其他项目组。
        # 1）work stealing，当本线程无可运行的 G 时，尝试从其他线程绑定的 P 偷取 G，而不是销毁线程。
        # 2）hand off，当本线程因为 G 进行系统调用阻塞时，线程释放绑定的 P，把 P 转移给其他空闲的线程执行。

        - GMP 偷取 G 为什么不需要加锁？ # 因为是CAS操作，注意这里不是“锁”的逻辑（不是锁G），而是不需要锁，直接获取就可以了（“当一个P想要从另一个P的本地G队列中“偷取”Goroutine时，它会使用CAS操作来尝试从队列的头部移除一个Goroutine”）。

        - "***golang goroutine leak的常见场景? ?***" # [跟读者聊 Goroutine 泄露的 N 种方法，真刺激！](https://mp.weixin.qq.com/s/ql01K1nOnEZpdbp--6EDYw)

        - GMP主动调度的抢占时机，有GC STW、syscall、时间片用完、goroutine被阻塞在unbuffered chan ，，请分别介绍这4种情况的具体过程? # GC(GC的STW是一个GMP主动调度的抢占时机), syscall(goroutine执行syscall时，Go调度器会主动选择一个合适的时机来调度其他可运行的goroutine), 时间片用尽（类似kernel里CFS tick的时候，也是最常见的抢占时机）
        - goroutine OOM了，会发生什么？怎么处理？
        - 如果大量goroutine OOM掉，体现在pprof火焰图上是什么样的？

        - "***怎么控制goroutine数量?***" # [控制协程(goroutine)的并发数量 | Go 语言高性能编程 | 极客兔兔](https://geektutu.com/post/hpg-concurrency-control.html)

        - "***根据用户请求关闭协程的代码怎么写? 协程开启和退出时，都发生了什么?***" # [回答我，停止 Goroutine 有几种方法？](https://mp.weixin.qq.com/s?__biz=MzI4NDM0MzIyMg==&mid=2247487342&idx=1&sn=55bd81190863d693f71657fce5ed454b)
        #  chan, context
        #  - chan: 通过 chan 的 close 机制来关闭协程
        #  - 定期轮询 chan，来管理协程
        #  - context，监听 context 的 Done 信号，就可以控制关闭多个协程了

        - time.Timer 定时器（time.Timer、time.Ticker、time.After 和 time.AfterFunc 的相关用法） # [Golang 定时器使用方法汇总](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651444999&idx=4&sn=f5b8eea547b2c967aeaca4bd28df8c8e) ticker 和 timer 都是定时器，原理也类似; ticker 是一个周期触发定时器，按给定 interval 往 chan 中发送当前时间，而 chan 的接受者以固定 interval 从 chan 中读取;

        - 协程还是线程更适合IO密集应用还是CPU密集应用？
        # 结论：协程更适用于IO密集应用，线程更适用于CPU密集应用。
        # IO密集型应用：协程非常适合IO密集型应用，因为它们可以在等待IO操作时挂起，从而提高应用程序的并发性和响应性。高并发低资源消耗：协程适合需要高并发但资源消耗较低的应用场景。
        # CPU密集型应用：线程适合CPU密集型应用，因为它们可以利用多核处理器的并行处理能力。阻塞性操作：对于包含阻塞性操作的应用，线程可能更合适，因为操作系统可以决定何时调度线程，以及如何在它们之间共享CPU时间。

        - 协程的调度为什么是随机的？

        - goroutine 的状态（9种） 切换 # [goroutine 的状态 切换](https://huizhou92.com/zh-cn/p/goroutine-%E7%9A%84%E7%8A%B6%E6%80%81-%E5%88%87%E6%8D%A2/)





    - topic: "***golang map（hashmap实现、哈希冲突、读写机制（遍历过程、扩容机制））***"
      pic:
        - https://docs.lucc.dev/images/langs/golang/map.png
        - https://docs.lucc.dev/images/langs/golang/bmap-ds.png
      qs:
        - 参照golang map源码就可以看到，hmap里 （hash0 就是 hash func，buckets）、（oldbuckets 就是双桶）、（B 就是 load factor），除此之外，就是 count, flags, noverflow, nevacuate
        - 可以看到bmap的kv不是 k/v/k/v/... 这种格式，而是 k/k/v/v/... 这种格式，是为了节省内存。因为k/v这种格式的话，每组之间都需要padding，如果 k/k/v/v 的话，则每个bucket共享一个padding即可
        - 每个bucket(也就是bmap)只存储8个kv，超出就放到新bucket # 哈希表的每个桶都只能存储 8 个kv，一旦当前哈希的某个桶超出 8 个，新的kv就会存储到哈希的溢出桶中。随着键值对数量的增加，溢出桶的数量和哈希的装载因子也会逐渐升高，超过一定范围就会触发扩容，扩容会将桶的数量翻倍，元素再分配的过程也是在调用写操作时增量进行的，不会造成性能的瞬时巨大抖动。

        - hmap：hmap struct 中有哪些是主要字段？hmap 的 extra 有啥用？双桶, mapextra 用来优化 GC,
        - hmap 中的负载因子 B 是什么？为什么是 6.5？负载因子如何影响map的性能？溢出率 # LB是最大可容纳元素数，用来衡量当前哈希表中空间利用率的核心指标。是因为负载因子太大了，会有很多溢出的桶；太小了，就会浪费很多空间。

        - bmap：bmap 是什么？为什么golang的map还需要bmap？ bucket. tophash 数组的设计加速了 key 的查找过程，tophash 也被复用来标记扩容操作时的状态
        - 通过 hash 函数获取目标 key 的哈希，哈希和数组的长度通过位运算，获取数组位置的索引。遍历 bmap 里的 key，和目标 key 对比获取 key 的索引。根据 key 的索引通过计算偏移量，获取到对应 value。 # 用位运算转换求余操作，m % n，当 `n = 1 << B` 的时候，可以转换成 `m & (1 << B - 1)`
        - mapassign

        - 为啥 SwissMapType 和之前map的实现的区别在于哈希冲突的解决方案，开放寻址法（linear probing）的优势在于缓存友好，劣势在于查找性能退化和无法删除，那SwissMapType是怎么解决起劣势的呢?

        - "***map扩容机制? 什么情况下会扩容，怎么扩容? 查看 hashGrow() 以及 渐进式扩容 growWork()***" # 当 map 的负载因子超过预设的阈值（通常是 6.5）或者溢出桶（overflow buckets）数量过多时，Go 会触发 map 的扩容机制。

        - map 有缩容机制吗？为啥说“golang map是伪缩容”
        # 因为map 的扩缩容的主要区别在于 hmap.B 的容量大小改变。而缩容由于 hmap.B 压根没变，内存空间的占用也是没有变化的（具体来说就是，在删除元素时，并不会释放内存）。所以一定不要往 golang 的 map 中塞入太多数据。map 没有缩容机制，map 基于拉链法实现，里面的元素如果被大批量的删除后，会触发等量扩容。等量扩容时会申请原有大小一样的内存块，渐进式的扩容过去，让原有的 map 中因为很多元素被删除后导致元素排序稀疏的情况经过 rehash 后会排序会变得紧密，减少溢出桶的使用。map 目前是伪缩容，仅针对溢出桶过多的情况；如果触发缩容，hash 数组占用的内存大小不变。
        # 伪缩容，因为map 的扩缩容的主要区别在于 hmap.B 的容量大小改变，而缩容由于 hmap.B 压根没变，内存空间的占用也是没有变化的（具体来说就是，在删除元素时，并不会释放内存），所以一定不要往 golang 的 map 中塞入太多数据。
        # 若是扩容，则 bigger 为 1，也就是 B+1。代表 hash 表容量扩大 1 倍。不满足就是缩容，也就是 hash 表容量不变。可以得出结论：map 的扩缩容的主要区别在于 hmap.B 的容量大小改变。而缩容由于 hmap.B 压根没变，内存空间的占用也是没有变化的。

        - "***遍历过程：key为啥无序? map 在遍历时，并不是从 0 号 bucket 开始遍历，而是从一个随机 bucket 的随机 cell 开始遍历***"
        - golang中map进行 读操作 的具体过程(查找数据的具体流程)? # key 经过 hash 后共 64 位，根据 hmap 中 B 的值，计算它到底要落在哪个桶时，桶的数量为 2^B，如 B=5，那么用 64 位最后 5 位表示第几号桶，在用 hash 值的高 8 位确定在 bucket 中的存储位置，当前 bmap 中的 bucket 未找到，则查询对应的 overflowbucket，对应位置有数据则对比完整的哈希值，确定是否是要查找的数据。如果当前 map 处于数据搬移状态，则优先从oldbuckets 查找。
        - "***golang map 怎么实现顺序读取?***" # 核心思路都是通过“线性数据结构”实现有序（map+线性数据结构），读取数据时按照这个有序的字段进行读取就可以了。只不过线性数据结构的选择不同，一个选择list，另一个选择slice。 [HashMap](

        - golang中map进行 写操作 的具体过程?

        - golang 修改map中的元素：为啥如果map里不是struct（而是普通value）的话，就可以直接修改；如果是map of struct的话，就不能修改啊？那怎么修改map of struct中的数据呢? # [Kimi.ai - 帮你看更大的世界](https://kimi.moonshot.cn/chat/csgv596aofothh29ut30) 归根到底还是值类型和引用类型的问题。修改key对应的val时，二者都是修改该val的副本，但是struct需要修改该val副本的字段，并将副本重新赋值给map，才能实现对map中struct的修改。

        - 为什么 map 增删改会触发标志位，导致 panic？

        - "***怎么用map实现并发读写? (比较 mutex+map 和 sync.Map 两种方案)***" # sync.Map：具体来说，就是冗余了两个数据结构，分别是：read 和 dirty，减少加锁对性能的影响。map+mutex：只有一把锁，会导致大量的争夺锁，导致各种冲突和性能低下




    - topic: sync.Map
      url: https://github.com/golang/go/blob/master/src/sync/map.go
      what:
        - 概念：sync.Map 的优缺点（或者说特点）？ # 其实没有优缺点，只有特点。因为优缺点本身就无法兼顾，是个tradeoff。
        #- *通过读写分离，降低锁时间来提高效率，尤其适合读多写少的场景（这也就表现为 sync.Map 不适于大量写的场景（读操作性能好）*，这样会导致 read map 时读不到数据而进一步加锁读取，而 dirty map 也会一直晋升为 read map，整体性能较差）*
        #- 空间换时间。通过冗余的两个数据结构 (read、dirty)，实现加锁对性能的影响
        #- 使用只读数据 (read)，避免读写冲突。
        #- 动态调整，miss 次数多了之后，将 dirty 数据提升为 read。
        #- double-checking。
        #- 延迟删除。删除一个 kv 只是打标记，只有在提升 dirty 的时候才清理删除的数据。
        #- 优先从 read 读取、更新、删除，因为对 read 的读取不需要锁。
      hti:
        - 实现：sync.Map 的具体实现？基本操作的具体流程？
        #- read 和 dirty 是共享内存的，尽量减少冗余内存的开销（实际上sync.Map 有两个 map 构成，一个用于读（dirty），一个用于写（read）。用于写的叫 dirty，采用互斥锁进行加锁，对于只读的数据会先读提供读的 map，然后才会去读 dirty。为了优化 sync.Map 的性能，还提供了一个 missed 计数，用于来决策何时将 dirty 中的元素变成只读的 map 元素等操作）。
        #- read 是原子性的，可以并发读，写需要加锁。
        #- 读的时候先 read 中取，如果没有则会尝试去 dirty 中读取（需要有标记位 readOnly.amended 配合）
        #- dirty 就是原生 Map 类型，需要配合各类锁读写。
        #- 当 read 中 miss 次数等于 dirty 长度时，dirty 会提升为 read，并且清理已经删除的 k-v（延迟更新，具体如何清理需要 enrty 中的 p 标记位配合）
        #- 双检查（在加锁后会再次对值检查一遍是否依然符合条件）
        #- sync.Map 没有提供获取长度 size 的方法，需要通过遍历来计算。

        - "***How does sync.Map implemented? 能否认为sync.Map是read-preferring呢?***" # 使用了读写分离来去保证线程安全的，sync.map 的数据结构分为读 map(read)、写 map(dirty)、还有mutex以及一个记录穿透次数的值(misses)。具体实现是每次读取都会先读取读部分的 kv，没有则去读写部分的 kv(操作写部分时都会上锁)。当穿透到写部分的次数大于写部分的长度时，就会将写部分同步到读部分，并且把写部分清空。所以多协程下一般都会先打到无锁的读部分，这能保证读取性能。

        - entry的p可能的状态，有哪些？ # entry 的 p 可能的状态包括 nil（空值）、指向正常数据的指针、指向脏数据的指针、锁定状态的指针、等待状态的指针和过期状态的指针。

        - sync.Map是用来保证golang map并发读还是并发写的？不是说golang map天然支持并发读嘛？
        #  在Go语言中，原生的map类型并不是并发安全的，这意味着在并发环境下直接使用map进行读写操作可能会导致数据竞争和程序崩溃。对于并发读的情况，Go 1.6之前，内置的map类型是部分goroutine安全的，并发的读没有问题，但并发的写可能有问题。自Go 1.6之后，并发地读写map会报错，这是因为Go语言的运行时会在检测到并发读写map时抛出fatal error。因此，并不是说Go语言的map天然支持并发读，而是在Go 1.6之后，Go语言明确禁止了并发读写map的行为，以避免数据竞争和潜在的程序错误。
        #  总结来说，Go语言的map在并发环境下并不是安全的，无论是读还是写。为了在并发环境下安全地使用map，可以使用sync.Map，或者通过加锁机制（如sync.Mutex或sync.RWMutex）来保护对map的访问。



    - topic: pprof
      what:
        - pprof 应该监控 RSS、PSS 和 USS 中的哪个内存指标？
        #  *结论：监控哪个都一样*
        #  - golang1.12 开始将 madvise 系统调用 使用的内存回收策略从 MADV_DONTNEED 改为了 MADV_FREE，而 RSS 作为最常用的内存监控指标，不会反映出进程中未被 OS 回收的那部分内存，导致了很多监控误报的问题
        #  - VSS 反应了当前进程申请且未归还的虚拟地址空间，RSS 包含了所谓共享库，PSS 将共享库的大小按 共享进程的比例进行了均摊，而 USS 直接没计算共享库的内存。
        #  - 这定义来看，无论是 RSS、PSS 还是 USS，它们的区别都只在共享库上，但对于像 Go 这种静态链接 来的程序，共享库并不那么常见。一个合理的怀疑就是大部分情况下：RSS == PSS == USS
        - "***大部分profiling工具会提供哪些分析器（CPU分析器、堆分析器、GPU分析器、互斥锁分析器、IO分析器、语言分析器）？***"
        #  1、CPU 分析器：跟踪程序中每个函数或代码块的运行时间，记录函数调用的堆栈信息，生成调用图，显示函数之间的调用关系和时间分布。工程师利用调用图，可以找出 CPU 使用率最高的代码段，集中优化这些“热点”部分。
        #  2、堆分析器（Heap Profiler）：监控程序的内存使用情况，帮助定位内存泄漏或不必要的内存分配。例如，在 Java 应用中，使用 Heap Profiler 定位导致内存溢出的具体对象。
        #  3、GPU 分析器：分析 GPU 的使用情况，常见于游戏开发或其他图形密集型应用场景，用于优化渲染性能。
        #  4、互斥锁分析器：检测程序中互斥锁的竞争情况，帮助优化线程间的并发性能，减少因锁争用导致的性能瓶颈。
        #  5、I/O 分析器：评估 I/O 操作的性能，包括文件读写延迟和网络请求耗时，帮助识别数据传输瓶颈并提升效率。
        #  6、特定编程语言的分析器：例如 JVM Profiler，专门分析运行在 Java 虚拟机上的应用程序，挖掘与语言特性相关的性能问题。
        #  过去，由于分析器资源消耗较高，工程师通常仅在紧急情况下临时启用它们。随着低开销分析技术的兴起，如编程语言层面的 Java Flight Recorder 和 Async Profiler 技术、操作系统层面的 systemTap 和 eBPF 技术，让生产环境中的“持续性能分析”（Continuous Profiling）逐渐成为现实，捕获“疑难杂症”的现场快照也变得更加容易。
      htu:
        - pprof 的各项参数
        - 怎么用pprof快速定位内存泄漏的代码？
        - pprof 指标 metrics  profile(CPU profile), heap, (allocs, block, goroutine, mutex, , threadcreate)


    - topic: "***golang代码常用写法***（comma-ok, options模式, builder模式, private-struct(avoid call), Callback as param）"
      qs:
        - 逗号 ok 模式怎么用？ # 判断 error；判断一个 channel 是否关闭；判断 slice 中的 value 是否存在；判断 map 的 key 是否存在；

        # [darjun/ghtrending: API to fetch github trending](https://github.com/darjun/ghtrending/blob/main/option.go)
        - 函数选项 option 模式（Functional Options Pattern）：options 模式解决了什么问题？什么场景适合用 options 模式？ # [一些实用的编程模式 | Options模式](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247489777&idx=1&sn=a9c17cc31cb77f9139a45c484057f7ac)

        - How to use "Builder Pattern"? # [一些实用的编程模式 | Builder模式能用来解决什么问题？](https://mp.weixin.qq.com/s/kpuCZgwzWk1PD4UrFlazuQ)

        # [Golang必备技巧：接口型函数 | 飞雪无情的博客](https://www.flysnow.org/2016/12/30/golang-function-interface)
        # [pinentry-touchid/main.go at main · jorgelbg/pinentry-touchid](https://github.com/jorgelbg/pinentry-touchid/blob/main/main.go)
        - type func的写法

        - "***怎么把函数作为参数来传递？声明 Callback 的 type，并实现该方法，把 Callback 作为参数即可***" # 我的 actions() 和 batch() 都需要复用 Xz()，只有 ExtractQuestion() 需要替代，所以就把 func() 作为参数使用

        # [Safer Enums in Go](https://threedots.tech/post/safer-enums-in-go/)
        - enum string 的写法（也就是 `type Role string`），非常实用 # https://github.com/mritd/touchid/blob/master/touchid.go
        - 【enum定义常量】golang 里用 const + iota 适用于什么场景？除了有大量const，又懒得逐个定义以外，还有哪些呢？如果没有显示声明，是否会导致语义不清晰呢？


        - 【怎么理解“用组合代替继承”】如何应对不断膨胀的接口 # [如何应对不断膨胀的接口 | 董泽润的技术笔记](https://mytechshares.com/2022/04/06/how-to-handle-expand-interface/)

        # [谈一谈golang接口和结构体的三种嵌套及匿名字段谈一谈golang接口和结构体的三种嵌套及匿名字段 1.结构体嵌套结构 - 掘金](https://juejin.cn/post/7120219197799563278)
        - struct嵌套struct? # [Go 语言中结构体嵌入结构体？](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651454164&idx=1&sn=36e6184e6e050ea89a97e35036d818a9#rd)
        - struct嵌套interface? # [Golang 中 struct 嵌入 interface - Alan's Blog](https://linxuyalun.github.io/2021/02/18/golang-struct-with-embeded-interface/)
        - interface嵌套interface? # [Go 语言中接口嵌入接口](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651454172&idx=1&sn=ee009c0714c29c5fd18dfb9b02d066f1#rd)
        - Please implement factory pattern using golang. In other words, implement







    - topic: golang错误处理
      isFold: true
      why:
        - 【显式错误控制】强制逐层检查错误返回值，避免未处理异常导致进程崩溃
        - 【上下文可追溯】分层包装错误（DAO→Logic→Controller），形成完整错误树而非碎片化信息
      what:
        - 【错误树结构】Go 1.20+支持多错误包装（`%w`），用树状结构替代线性链，精准定位根源
        - 【错误类型系统】内置`error`接口，支持哨兵错误（`io.EOF`）、自定义错误（结构体+`Error()`方法）
      ww:
        - 【DAO层原始错误】数据库操作返回未包装错误（如`sql.ErrNoRows`），供上层决策是否需转换为业务错误
        - 【Logic层业务包装】添加领域上下文，区分技术错误与业务规则 # 如 fmt.Errorf("订单创建失败: %w", err)
        - 【Controller层统一处理】记录日志/返回用户友好信息，禁止透传底层细节（如数据库路径）
      htu:
        - 【错误包装规范】Logic层调用DAO时必用`fmt.Errorf`或`errors.Wrap`，禁用`return err`
        - 【错误检查实践】用`errors.Is(err, targetErr)`替代`==`，兼容被包装的错误
        - 【Panic安全边界】仅在不可恢复错误（如端口占用）时`panic`，且需`recover`中间件捕获
      hti:
        - 【分层错误接口】DAO层返回基础错误，Logic层定义业务错误类型（如`type OrderError struct{...}`）
        - 【错误日志聚合】通过`logrus`/`zap`结构化日志，在最外层统一记录错误树（`log.Errorf("%+v", err)`）
      hto:
        - 【性能优化】高频错误定义全局哨兵变量（`var ErrTimeout = errors.New(...)`），减少内存分配
        - 【安全加固】包装错误时过滤敏感信息（如`"数据库错误"`替代`"连接postgres:pass@host失败"`）


#  # [Go错误处理最佳实践 - Mohuishou](https://lailin.xyz/post/go-training-03.html)
#  - "***golang错误处理：golang项目中（如果不分层）应该怎么进行错误处理? 如果分层（比如经典的CLD三层）呢?***" # [3种方式！Go Error处理最佳实践](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453360&idx=1&sn=ec479badda69e92d0f1f52e4bc00358b)
#  #  ***业务代码中不应该出现panic***，并且如果存在panic，应该主动recover处理（通常都是实现一个recover中间件，来catch所有的panic，不需要手动逐个处理）
#  #  golang 处理 error 则直接参考 [crunchy/errors.go at master · muesli/crunchy](https://github.com/muesli/crunchy/blob/master/errors.go)
#  #  相比于普通的pkg中的错误处理，***golang项目有了分层，所以唯一需要注意的就是在各层用 `errors.Wrap()` 对err进行包装（追加stack以及相应的错误提示），而非直接 `return err`***，以便形成 error tree，及时发现问题。正如 [Don’t return err in Go — akavel's digital garden](https://akavel.com/go-errors) 中提到的，也就是 ***错误处理的原则就是：***
#
#  - golang项目分层（比如经典的CLD三层 (Controller-Logic-DAO) 或者说CSD三层）中怎么使用 Error?
#
#  - 分清楚什么情况下应该使用 Error, Exception, Panic。为啥“golang 没有 exception，只有error和panic”？基于什么考虑进行这种设计 # [如何看待gopanic及异常处理 - MySpace](https://www.hitzhangjie.pro/blog/2021-04-16-%E5%A6%82%E4%BD%95%E7%9C%8B%E5%BE%85gopanic%E5%8F%8A%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86/) # 首先要分清楚Error, Exception和panic。go 源代码很多地方写 panic, 但是工程实践业务代码不要主动写 panic，理论上 panic 只存在于 server 启动阶段，比如 config 文件解析失败，端口监听失败等等，所有业务逻辑禁止主动 panic，所有异步的 goroutine 都要用 recover 去兜底处理。 “异常+try-catch，本质上将当前操作的错误处理逻辑转换为了caller要解决的问题，并没有少写多少错误处理代码，反而，同一异常处理代码在多个try-catch中被拷贝，而且可读性更差了。错误发生地、错误处理地分散在不同地方，能说是可读性好吗？我不这么认为。”
#  - golang 中怎么使用 Error?
#
#  - 自定义错误记得要实现 error 接口
#  - 怎么 wrapping 多个 errors？
#  - 不要直接return err，应该使用fmt.Errorf()自定义error，自己封装一个newError # [Don’t return err in Go — akavel's digital garden](https://akavel.com/go-errors)
#  - 怎么包装err? 应该是“错误树”而不是“错误链” # [errors: add support for wrapping multiple errors · Issue #53435 · golang/go](https://github.com/golang/go/issues/53435) "We replace the term "error chain" with "error tree"." 所以应该是“错误树”而不是“错误链”，*因为golang1.20之后支持一次包装多个错误，于是错误之间可以建立 tree关系，而非之前的线性关系了*
#  - golang中用errors.New(), errors.Is() 怎么判断err是否相同？ # 需要注意errors.Is()判断err是否相同时，需要两个参数都是直接引用var声明的err，如果只是err的text相同，会返回false。这个应该是基本常识了。
#  - 操作数据库时，比如 dao 层中遇到一个 sql.ErrNoRows 时，是否应该 wrap 这个 error，抛给上层；为什么？应该怎么做？请写出代码 # [关于Golang错误处理的一些建议 · Issue #66 · kevinyan815/gocookbook](https://github.com/kevinyan815/gocookbook/issues/66) 总结一下，错误处理的原则就是：错误只在逻辑的最外层处理一次，底层只返回错误。底层除了返回错误外，要对原始错误进行包装，增加错误信息、调用栈等这些有利于排查的上下文信息。
#
#  # [如何在 Golang 项目中处理好错误 | ZhengHe](https://zhenghe-md.github.io/blog/2020/10/05/Go-Error-Handling-Research/)
#  - boltdb 的作者之前说过一句很精辟的话“有三种角色在消费错误，它们分别是用户 (end user)、程序 (application) 和运维 (operator)”，换个角度更具体的说法是 “程序底层 （Dao、基础设施层） 抛出错误，程序中层（领域服务层、应用服务层）包装错误，程序上层（控制层） 记录错误。”，归根到底以上内容都是为了解决 哪些error需要写logging，哪些不需要。 #
#
#  - golang的一个很经典的，需要注意的写法：善用vars，把error、regex之类的东西，都抽出来，放到vars里。 # 比如ErrWatermarkTooLarge = errors.New("水印太大") 之类的
#
#  - "`err == io.EOF` 和 `errors.Is(err, io.EOF)` 这两种写法有啥区别"
